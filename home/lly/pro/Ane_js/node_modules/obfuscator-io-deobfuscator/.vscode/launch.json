{"version": "0.2.0", "configurations": [{"name": "Debug CLI (TypeScript)", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/cli.ts", "args": ["${workspaceFolder}/example.js", "-o", "${workspaceFolder}/output.js"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "runtimeArgs": ["-r", "ts-node/register"], "env": {"NODE_ENV": "development", "TS_NODE_COMPILER_OPTIONS": "{\"sourceMap\":true}"}, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"]}, {"name": "Debug CLI (Compiled JS)", "type": "node", "request": "launch", "program": "${workspaceFolder}/dist/cli.js", "args": ["${workspaceFolder}/example.js", "-o", "${workspaceFolder}/output.js"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "preLaunchTask": "npm: prepare"}, {"name": "Debug Test (TypeScript)", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/test.ts", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "runtimeArgs": ["-r", "ts-node/register"], "env": {"NODE_ENV": "development", "TS_NODE_COMPILER_OPTIONS": "{\"sourceMap\":true}"}, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"]}, {"name": "Debug Test (Compiled JS)", "type": "node", "request": "launch", "program": "${workspaceFolder}/dist/test.js", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**"], "preLaunchTask": "npm: test"}, {"name": "Debug String Revealer", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/cli.ts", "args": ["${workspaceFolder}/example.js", "-o", "${workspaceFolder}/output.js"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "runtimeArgs": ["-r", "ts-node/register"], "env": {"NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**"], "stopOnEntry": false, "cwd": "${workspaceFolder}"}, {"name": "Debug CLI (TS with Source Maps)", "type": "node", "request": "launch", "program": "${workspaceFolder}/dist/cli.js", "args": ["${workspaceFolder}/example.js", "-o", "${workspaceFolder}/output.js"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "sourceMaps": true, "smartStep": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "preLaunchTask": "typescript: build - tsconfig.json"}, {"name": "Attach to Node Process", "type": "node", "request": "attach", "port": 9229, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"]}]}