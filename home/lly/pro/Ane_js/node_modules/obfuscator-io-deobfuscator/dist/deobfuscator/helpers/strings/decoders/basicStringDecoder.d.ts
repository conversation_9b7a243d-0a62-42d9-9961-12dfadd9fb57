import { DecoderType, StringDecoder } from './stringDecoder';
export declare class BasicStringDecoder extends StringDecoder {
    /**
     * Returns the type of the decoder.
     */
    get type(): DecoderType;
    /**
     * Decodes a string.
     * @param index The index.
     */
    getString(index: number): string;
    /**
     * Decodes a string for the rotate string call.
     * @param index The index.
     * @returns THe string.
     */
    getStringForRotation(index: number): string;
}
