"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicStringDecoder = void 0;
const stringDecoder_1 = require("./stringDecoder");
class BasicStringDecoder extends stringDecoder_1.StringDecoder {
    /**
     * Decodes a string.
     * @param index The index.
     */
    getString(index) {
        return this.stringArray[index + this.indexOffset];
    }
}
exports.BasicStringDecoder = BasicStringDecoder;
