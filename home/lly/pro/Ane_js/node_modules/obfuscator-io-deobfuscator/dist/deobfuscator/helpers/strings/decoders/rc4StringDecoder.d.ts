import { DecoderType, StringDecoder } from './stringDecoder';
export declare class Rc4StringDecoder extends StringDecoder {
    private readonly stringCache;
    /**
     * Creates a new RC4 string decoder.
     * @param stringArray The string array.
     * @param indexOffset The offset used when accessing elements by index.
     */
    constructor(stringArray: string[], indexOffset: number);
    /**
     * Returns the type of the decoder.
     */
    get type(): DecoderType;
    /**
     * Decodes a string.
     * @param index The index.
     */
    getString(index: number, key: string): string;
    /**
     * Decodes a string for the rotate string call.
     * @param index The index.
     * @returns THe string.
     */
    getStringForRotation(index: number, key: string): string;
    /**
     * Decodes a string encoded with RC4.
     * @param str The RC4 encoded string.
     * @param key The key.
     * @returns The decoded string.
     */
    private rc4Decode;
}
