"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProperty = exports.setProperty = exports.copyExpression = void 0;
const generator_1 = __importDefault(require("@babel/generator"));
const parser_1 = require("@babel/parser");
/**
 * Copies an expression.
 * @param expression The expression.
 * @returns The copy.
 */
const copyExpression = (expression) => {
    return (0, parser_1.parseExpression)((0, generator_1.default)(expression).code);
};
exports.copyExpression = copyExpression;
/**
 * Sets a property on an object.
 * @param obj The object.
 * @param property The property key.
 * @param value The value.
 */
const setProperty = (obj, property, value) => {
    obj.property = value;
};
exports.setProperty = setProperty;
/**
 * Gets the value of a property on an object.
 * @param obj The object.
 * @param property The property key.
 * @returns
 */
const getProperty = (obj, property) => {
    return obj.property;
};
exports.getProperty = getProperty;
//# sourceMappingURL=misc.js.map