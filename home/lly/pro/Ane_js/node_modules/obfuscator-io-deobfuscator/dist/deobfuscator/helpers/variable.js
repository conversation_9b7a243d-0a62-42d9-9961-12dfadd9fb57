"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstantAssignmentVariable = exports.ConstantDeclarationVariable = exports.ConstantVariable = void 0;
exports.findConstantVariable = findConstantVariable;
const t = __importStar(require("@babel/types"));
class ConstantVariable {
    /**
     * Creates a new constant variable.
     * @param name The name of the variable.
     * @param binding The binding.
     * @param expression The value the variable holds.
     */
    constructor(name, binding, expression) {
        this.name = name;
        this.binding = binding;
        this.expression = expression;
    }
}
exports.ConstantVariable = ConstantVariable;
class ConstantDeclarationVariable extends ConstantVariable {
    /**
     * Creates a new constant variable that is declared and initialised immediately.
     * @param declaratorPath The path of the variable declarator.
     * @param name The name of the variable.
     * @param binding The binding.
     * @param expression The value the variable holds.
     */
    constructor(declaratorPath, name, binding, expression) {
        super(name, binding, expression);
        this.declaratorPath = declaratorPath;
    }
    /**
     * Removes the variable.
     */
    remove() {
        this.declaratorPath.remove();
    }
}
exports.ConstantDeclarationVariable = ConstantDeclarationVariable;
class ConstantAssignmentVariable extends ConstantVariable {
    /**
     * Creates a new constant variable that is declared with no value then assigned to later.
     * @param declaratorPath The path of the variable declarator.
     * @param assignmentPath The path of the assignment to the variable.
     * @param name The name of the variable.
     * @param binding The binding.
     * @param expression The value the variable holds.
     */
    constructor(declaratorPath, assignmentPath, name, binding, expression) {
        super(name, binding, expression);
        this.declaratorPath = declaratorPath;
        this.assignmentPath = assignmentPath;
    }
    /**
     * Removes the variable.
     */
    remove() {
        this.declaratorPath.remove();
        // only safe to remove an assignment if the parent doesn't rely on it
        if (this.assignmentPath.parentPath &&
            this.assignmentPath.parentPath.isExpressionStatement()) {
            this.assignmentPath.remove();
        }
        else {
            this.assignmentPath.replaceWith(this.expression);
        }
    }
}
exports.ConstantAssignmentVariable = ConstantAssignmentVariable;
/**
 * Checks whether a node is initialising a 'constant' variable and returns the variable if so.
 * @param path The path.
 * @param isType The function that determines whether the expression is of the desired type.
 * @returns The constant variable or undefined.
 */
function findConstantVariable(path, isType, canBeFunction = false) {
    if (path.isVariableDeclarator() &&
        t.isIdentifier(path.node.id) &&
        path.node.init != undefined &&
        isType(path.node.init)) {
        const name = path.node.id.name;
        const binding = path.scope.getBinding(name);
        return binding && isConstantBinding(path, binding)
            ? new ConstantDeclarationVariable(path, name, binding, path.node.init)
            : undefined;
    }
    // essentially same as declarator but allows function declarations
    else if (canBeFunction &&
        path.isFunctionDeclaration() &&
        t.isIdentifier(path.node.id) &&
        isType(path.node)) {
        const name = path.node.id.name;
        const binding = path.scope.getBinding(name);
        return binding && isConstantBinding(path, binding)
            ? new ConstantDeclarationVariable(path, name, binding, path.node)
            : undefined;
    }
    else if (path.isAssignmentExpression() &&
        path.node.operator == '=' &&
        t.isIdentifier(path.node.left) &&
        isType(path.node.right)) {
        const name = path.node.left.name;
        const binding = path.scope.getBinding(name);
        return binding && isConstantAssignedBinding(path, binding)
            ? new ConstantAssignmentVariable(binding.path, path, name, binding, path.node.right)
            : undefined;
    }
    return undefined;
}
/**
 * Returns whether a binding is constant for our purposes. Babel views
 * 'var' declarations within loops as non constants so this acts as a fix
 * for that.
 * @param path The path.
 * @param binding The binding.
 * @returns Whether.
 */
function isConstantBinding(path, binding) {
    return (binding.constant ||
        (binding.constantViolations.length == 1 &&
            path.node == binding.path.node &&
            path.node == binding.constantViolations[0].node));
}
/**
 * Returns whether a binding with a single assignment expression (separate
 * to the declaration) can be treated as constant.
 * @param path The path.
 * @param binding The binding.
 * @returns Whether.
 */
function isConstantAssignedBinding(path, binding) {
    if (((binding.path.isVariableDeclarator() && binding.path.node.init == undefined) ||
        binding.path.parentKey === 'params') && // either variable declarator with no initialiser or parameter of function
        binding.constantViolations.length === 1 &&
        binding.constantViolations[0].node === path.node) {
        const declarationParent = binding.path.isVariableDeclarator()
            ? binding.path.getStatementParent().parent
            : binding.path.parent.body;
        const parent = path.findParent(p => p.isStatement() || p.isConditionalExpression() || p.isLogicalExpression());
        if (!parent || !parent.isStatement() || parent.parent !== declarationParent) {
            return false;
        }
        return true;
    }
    else {
        return false;
    }
}
//# sourceMappingURL=variable.js.map