{"version": 3, "file": "rotation.js", "sourceRoot": "", "sources": ["../../../../../src/deobfuscator/helpers/strings/rotation/rotation.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuOA,8CA0BC;AAjQD,gDAAkC;AAElC,iDAA4D;AAG5D,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAG7D,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAExC,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC;IACzB,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,gBAAgB;CACnB,CAAC,CAAC;AAuBH;;;;;GAKG;AACH,SAAS,cAAc,CACnB,UAAwF,EACxF,UAAsC;IAEtC,QAAQ,UAAU,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,gBAAgB;YACjB,OAAO,kBAAkB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACtD,KAAK,iBAAiB;YAClB,OAAO,mBAAmB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACvD,KAAK,kBAAkB;YACnB,OAAO,oBAAoB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACxD,KAAK,gBAAgB;YACjB,OAAO,UAAU,CAAC;IAC1B,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAS,kBAAkB,CACvB,UAA4B,EAC5B,UAAsC;IAEtC,IACI,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC;QAClC,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,UAAU;QACpC,UAAU,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;QAChC,CAAC,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAC9C,CAAC;QACC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3C,IACI,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC;QAClC,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CACvB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,IAAA,qCAAwB,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CACpF,EACH,CAAC;QACC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CACtC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE,CAAS,CAAC,QAAQ,CAAC,KAAK,CACvF,CAAC;IACF,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC;IACpC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,EAAE,CAAC,CAAC;IACtD,CAAC;IAED,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAkB,CAAC;IACtD,OAAO;QACH,IAAI,EAAE,eAAe;QACrB,OAAO;QACP,IAAI;KACP,CAAC;AACN,CAAC;AAED;;;;;GAKG;AACH,SAAS,mBAAmB,CACxB,UAA6B,EAC7B,UAAsC;IAEtC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC7C,MAAM,IAAI,KAAK,CAAC,8BAA8B,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzE,CAAC;SAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACrD,MAAM,IAAI,KAAK,CAAC,8CAA8C,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IAC9F,CAAC;IAED,MAAM,QAAQ,GAAG,cAAc,CAAC,UAAU,CAAC,QAAe,EAAE,UAAU,CAAC,CAAC;IACxE,OAAO;QACH,IAAI,EAAE,gBAAgB;QACtB,QAAQ,EAAE,UAAU,CAAC,QAAyB;QAC9C,QAAQ;KACX,CAAC;AACN,CAAC;AAED;;;;;GAKG;AACH,SAAS,oBAAoB,CACzB,UAA8B,EAC9B,UAAsC;IAEtC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC9C,MAAM,IAAI,KAAK,CAAC,+BAA+B,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC1E,CAAC;SAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACjD,MAAM,IAAI,KAAK,CAAC,8CAA8C,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1F,CAAC;SAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;QAClD,MAAM,IAAI,KAAK,CAAC,8CAA8C,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED,MAAM,IAAI,GAAG,cAAc,CAAC,UAAU,CAAC,IAAW,EAAE,UAAU,CAAC,CAAC;IAChE,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC,KAAY,EAAE,UAAU,CAAC,CAAC;IAClE,OAAO;QACH,IAAI,EAAE,iBAAiB;QACvB,QAAQ,EAAE,UAAU,CAAC,QAA0B;QAC/C,IAAI;QACJ,KAAK;KACR,CAAC;AACN,CAAC;AAED;;;;GAIG;AACH,SAAS,cAAc,CAAC,SAAoB;IACxC,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;QACrB,KAAK,eAAe;YAChB,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC;QAChC,KAAK,gBAAgB;YACjB,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC1C,KAAK,iBAAiB;YAClB,OAAO,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAC3C,KAAK,gBAAgB;YACjB,OAAO,SAAS,CAAC,KAAK,CAAC;IAC/B,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,SAAS,CAAC,IAAmB;IAClC,OAAO,QAAQ,CACV,IAAI,CAAC,OAAO,CAAC,oBAAiE,CAC3E,GAAG,IAAI,CAAC,IAAI,CACf,CACJ,CAAC;AACN,CAAC;AAED;;;;GAIG;AACH,SAAS,mBAAmB,CAAC,SAAyB;IAClD,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAEpD,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;QACzB,KAAK,GAAG;YACJ,OAAO,CAAC,QAAQ,CAAC;IACzB,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,oBAAoB,CAAC,SAA0B;IACpD,MAAM,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC5C,MAAM,KAAK,GAAG,cAAc,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAE9C,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;QACzB,KAAK,GAAG;YACJ,OAAO,IAAI,GAAG,KAAK,CAAC;QACxB,KAAK,GAAG;YACJ,OAAO,IAAI,GAAG,KAAK,CAAC;QACxB,KAAK,GAAG;YACJ,OAAO,IAAI,GAAG,KAAK,CAAC;QACxB,KAAK,GAAG;YACJ,OAAO,IAAI,GAAG,KAAK,CAAC;QACxB,KAAK,GAAG;YACJ,OAAO,IAAI,GAAG,KAAK,CAAC;IAC5B,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAC7B,KAAe,EACf,UAA8B,EAC9B,UAAsC,EACtC,SAAiB;IAEjB,MAAM,SAAS,GAAG,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAEzD,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,IAAI,EAAE,CAAC;QACV,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC;gBACrB,MAAM;YACV,CAAC;iBAAM,CAAC;gBACJ,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAG,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAG,CAAC,CAAC;QAC/B,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACxE,CAAC;IACL,CAAC;AACL,CAAC"}