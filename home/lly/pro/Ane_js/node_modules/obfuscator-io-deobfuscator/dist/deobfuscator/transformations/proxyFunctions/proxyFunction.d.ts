import * as t from '@babel/types';
import { ConstantVariable } from '../../helpers/variable';
import { NodePath } from '@babel/traverse';
export type ProxyFunctionExpression = t.Function & {
    params: t.Identifier[];
    body: (t.BlockStatement & {
        body: {
            [0]: t.ReturnStatement & {
                argument: t.Expression | undefined;
            };
        };
    }) | t.Expression;
};
/**
 * Returns whether a proxy function expression.
 * @param node The node.
 * @returns Whether.
 */
export declare const isProxyFunctionExpression: (node: t.Node) => node is ProxyFunctionExpression;
type Argument = t.ArgumentPlaceholder | t.JSXNamespacedName | t.SpreadElement | t.Expression;
export declare class ProxyFunction {
    private readonly expression;
    /**
     * Creates a new proxy function.
     * @param expression The proxy function expression.
     */
    constructor(expression: ProxyFunctionExpression);
    /**
     * Returns the replacement for a call of the proxy function.
     * @param args The arguments of the call.
     * @returns The replacement expression.
     */
    getReplacement(args: Argument[]): t.Expression;
    /**
     * Replaces usages of the proxy function's parameters with the concrete arguments for a given call.
     * @param expression The expression.
     * @param args The arguments of the call.
     */
    private replaceParameters;
}
export declare class ProxyFunctionVariable extends ProxyFunction {
    private readonly variable;
    /**
     * Creates a new proxy function variable.
     * @param variable The variable.
     */
    constructor(variable: ConstantVariable<ProxyFunctionExpression>);
    /**
     * Returns the calls to the proxy function.
     * @returns The calls to the proxy function.
     */
    getCalls(): NodePath[];
    /**
     * Attempts to replace a call of the proxy function.
     * @param path The path of the call.
     * @returns Whether it was replaced.
     */
    replaceCall(path: NodePath): boolean;
}
export {};
