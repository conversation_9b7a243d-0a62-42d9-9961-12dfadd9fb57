"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectPacker = void 0;
const t = __importStar(require("@babel/types"));
const variable_1 = require("../../helpers/variable");
const transformation_1 = require("../transformation");
const traverse_1 = __importDefault(require("@babel/traverse"));
class ObjectPacker extends transformation_1.Transformation {
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log) {
        const self = this;
        (0, traverse_1.default)(this.ast, {
            enter(path) {
                const variable = (0, variable_1.findConstantVariable)(path, isEmptyObjectExpression);
                if (!variable) {
                    return;
                }
                const statementPath = path.getStatementParent();
                if (!statementPath ||
                    statementPath.parentPath == undefined ||
                    typeof statementPath.key != 'number') {
                    return;
                }
                const statements = statementPath.parentPath.node[statementPath.parentKey];
                const referencePathSet = new Set(variable.binding.referencePaths);
                let numRemoved = 0;
                for (let i = statementPath.key + 1; i < statements.length; i++) {
                    const node = statements[i];
                    if (t.isExpressionStatement(node) &&
                        self.isPropertyAssignment(node.expression, variable.name)) {
                        // replace multiple properties assigned in same statement
                        if (self.isPropertyAssignment(node.expression.right, variable.name)) {
                            const properties = [node.expression.left];
                            let right = node.expression.right;
                            while (self.isPropertyAssignment(right, variable.name)) {
                                properties.push(right.left);
                                right = right.right;
                            }
                            // don't duplicate expressions with side effects
                            if (!t.isLiteral(right)) {
                                break;
                            }
                            for (const { property } of properties) {
                                const isComputed = !t.isStringLiteral(property) &&
                                    !t.isNumericLiteral(property) &&
                                    !t.isIdentifier(property);
                                const objectProperty = t.objectProperty(property, right, isComputed);
                                variable.expression.properties.push(objectProperty);
                                self.setChanged();
                                numRemoved++;
                            }
                        }
                        else {
                            const key = node.expression.left.property;
                            const isComputed = !t.isStringLiteral(key) &&
                                !t.isNumericLiteral(key) &&
                                !t.isIdentifier(key);
                            // if the value contains a reference to the object itself then can't inline it
                            if (self.hasSelfReference(node.expression.right, statementPath, i, referencePathSet, log)) {
                                break;
                            }
                            const property = t.objectProperty(key, node.expression.right, isComputed);
                            variable.expression.properties.push(property);
                            self.setChanged();
                            numRemoved++;
                        }
                    }
                    else {
                        break;
                    }
                }
                statements.splice(statementPath.key + 1, numRemoved);
            }
        });
        return this.hasChanged();
    }
    /**
     * Searches a value for a reference to the object itself. Inlining this value
     * as an object property would be unsafe: https://github.com/ben-sb/obfuscator-io-deobfuscator/issues/39
     * @param value The value of the object property.
     * @param statementPath The path of the statement assigning the property.
     * @param arrayIndex The index of the assigning statement within the parent statement array.
     * @param referencePathSet A set of paths referencing the object being packed.
     * @returns Whether the value contains a reference to the object.
     */
    hasSelfReference(value, statementPath, arrayIndex, referencePathSet, log) {
        try {
            const valuePath = statementPath.parentPath.get(`${statementPath.parentKey}.${arrayIndex}`);
            let hasSelfReference = false;
            (0, traverse_1.default)(value, {
                Identifier(path) {
                    if (referencePathSet.has(path)) {
                        hasSelfReference = true;
                    }
                }
            }, valuePath.scope, undefined, valuePath);
            return hasSelfReference;
        }
        catch (err) {
            log(`Error looking for self reference when object packing: ${err}`);
            return false;
        }
    }
    /**
     * Returns whether a node is setting a property on a given object.
     * @param node The AST node.
     * @param objectName The name of the object.
     * @returns Whether.
     */
    isPropertyAssignment(node, objectName) {
        return (t.isAssignmentExpression(node) &&
            t.isMemberExpression(node.left) &&
            t.isIdentifier(node.left.object) &&
            node.left.object.name == objectName);
    }
}
exports.ObjectPacker = ObjectPacker;
ObjectPacker.properties = {
    key: 'objectPacking'
};
/**
 * Returns whether a node is an empty object expression.
 * @param node The node.
 * @returns Whether.
 */
const isEmptyObjectExpression = (node) => {
    return t.isObjectExpression(node) && node.properties.length == 0;
};
