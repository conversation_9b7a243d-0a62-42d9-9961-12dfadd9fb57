"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecoderType = exports.StringDecoder = void 0;
class StringDecoder {
    /**
     * Creates a new string decoder.
     * @param stringArray The string array.
     * @param indexOffset The offset used when accessing elements by index.
     */
    constructor(stringArray, indexOffset) {
        this.stringArray = stringArray;
        this.indexOffset = indexOffset;
        this.isFirstCall = true;
    }
}
exports.StringDecoder = StringDecoder;
var DecoderType;
(function (DecoderType) {
    DecoderType["BASIC"] = "BASIC";
    DecoderType["BASE_64"] = "BASE_64";
    DecoderType["RC4"] = "RC4";
})(DecoderType || (exports.DecoderType = DecoderType = {}));
//# sourceMappingURL=stringDecoder.js.map