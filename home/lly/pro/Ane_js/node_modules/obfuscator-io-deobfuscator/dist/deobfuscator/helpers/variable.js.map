{"version": 3, "file": "variable.js", "sourceRoot": "", "sources": ["../../../src/deobfuscator/helpers/variable.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmGA,oDA2CC;AA7ID,gDAAkC;AAElC,MAAsB,gBAAgB;IAKlC;;;;;OAKG;IACH,YAAY,IAAY,EAAE,OAAgB,EAAE,UAAa;QACrD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IACjC,CAAC;CAMJ;AArBD,4CAqBC;AAED,MAAa,2BAA8C,SAAQ,gBAAmB;IAGlF;;;;;;OAMG;IACH,YAAY,cAAgC,EAAE,IAAY,EAAE,OAAgB,EAAE,UAAa;QACvF,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;CACJ;AArBD,kEAqBC;AAED,MAAa,0BAA6C,SAAQ,gBAAmB;IAIjF;;;;;;;OAOG;IACH,YACI,cAAgC,EAChC,cAAgD,EAChD,IAAY,EACZ,OAAgB,EAChB,UAAa;QAEb,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;QAE7B,qEAAqE;QACrE,IACI,IAAI,CAAC,cAAc,CAAC,UAAU;YAC9B,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,qBAAqB,EAAE,EACxD,CAAC;YACC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;QACjC,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;CACJ;AAxCD,gEAwCC;AAID;;;;;GAKG;AACH,SAAgB,oBAAoB,CAChC,IAAc,EACd,MAAyB,EACzB,gBAAyB,KAAK;IAE9B,IACI,IAAI,CAAC,oBAAoB,EAAE;QAC3B,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,SAAS;QAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EACxB,CAAC;QACC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC5C,OAAO,OAAO,IAAI,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC;YAC9C,CAAC,CAAC,IAAI,2BAA2B,CAAI,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACzE,CAAC,CAAC,SAAS,CAAC;IACpB,CAAC;IACD,kEAAkE;SAC7D,IACD,aAAa;QACb,IAAI,CAAC,qBAAqB,EAAE;QAC5B,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EACnB,CAAC;QACC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC5C,OAAO,OAAO,IAAI,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC;YAC9C,CAAC,CAAC,IAAI,2BAA2B,CAAI,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC;YACpE,CAAC,CAAC,SAAS,CAAC;IACpB,CAAC;SAAM,IACH,IAAI,CAAC,sBAAsB,EAAE;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,GAAG;QACzB,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EACzB,CAAC;QACC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC5C,OAAO,OAAO,IAAI,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC;YACtD,CAAC,CAAC,IAAI,0BAA0B,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YACpF,CAAC,CAAC,SAAS,CAAC;IACpB,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,iBAAiB,CAAC,IAAc,EAAE,OAAgB;IACvD,OAAO,CACH,OAAO,CAAC,QAAQ;QAChB,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,IAAI,CAAC;YACnC,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI;YAC9B,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CACvD,CAAC;AACN,CAAC;AAED;;;;;;GAMG;AACH,SAAS,yBAAyB,CAC9B,IAAsC,EACtC,OAAgB;IAEhB,IACI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,SAAS,CAAC;QACzE,OAAO,CAAC,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC,IAAI,0EAA0E;QACtH,OAAO,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC;QACvC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAClD,CAAC;QACC,MAAM,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACzD,CAAC,CAAE,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAA4B,CAAC,MAAM;YACrE,CAAC,CAAE,OAAO,CAAC,IAAI,CAAC,MAAqB,CAAC,IAAI,CAAC;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC,mBAAmB,EAAE,CACjF,CAAC;QACF,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,MAAM,CAAC,MAAM,KAAK,iBAAiB,EAAE,CAAC;YAC1E,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;SAAM,CAAC;QACJ,OAAO,KAAK,CAAC;IACjB,CAAC;AACL,CAAC"}