import { LogFunction, Transformation, TransformationProperties } from '../transformation';
export declare class DeadBranchRemover extends Transformation {
    static readonly properties: TransformationProperties;
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log: LogFunction): boolean;
    /**
     * Returns whether a node is a literal or can be treated as one in the context of a test expression.
     * @param node The node.
     * @returns Whether.
     */
    private isSemiLiteral;
    /**
     * Returns whether a literal node is truthy.
     * @param literal The literal node.
     * @returns Whether.
     */
    private isTruthy;
}
