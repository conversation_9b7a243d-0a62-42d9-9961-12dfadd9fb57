{"version": 3, "file": "propertySimplifier.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/properties/propertySimplifier.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,+DAAqD;AACrD,sDAA0F;AAE1F,MAAa,kBAAmB,SAAQ,+BAAc;IAKlD;;;OAGG;IACI,OAAO,CAAC,GAAgB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAA,kBAAQ,EAAC,IAAI,CAAC,GAAG,EAAE;YACf,gBAAgB,CAAC,IAAI;gBACjB,IACI,IAAI,CAAC,IAAI,CAAC,QAAQ;oBAClB,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;oBACrC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAC/C,CAAC;oBACC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC5D,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBAC3B,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;YACL,CAAC;YACD,CAAC,6BAAoC,CAAC,CAClC,IAAiD;gBAEjD,IACI,IAAI,CAAC,IAAI,CAAC,QAAQ;oBAClB,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;oBAChC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAC1C,CAAC;oBACC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBAClD,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBAC3B,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;qBAAM,IACH,IAAI,CAAC,IAAI,CAAC,QAAQ;oBAClB,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EACzE,CAAC;oBACC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAC/B,CAAC;YACL,CAAC;SACJ,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;;AA7CL,gDA8CC;AA7C0B,6BAAU,GAA6B;IAC1D,GAAG,EAAE,wBAAwB;CAChC,CAAC"}