{"version": 3, "file": "deadBranchRemover.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/controlFlow/deadBranchRemover.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,+DAAuC;AACvC,sDAA0F;AAE1F,MAAa,iBAAkB,SAAQ,+BAAc;IAMjD;;;OAGG;IACI,OAAO,CAAC,GAAgB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAA,kBAAQ,EAAC,IAAI,CAAC,GAAG,EAAE;YACf,WAAW,CAAC,IAAI;gBACZ,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBAChC,MAAM,UAAU,GAAG,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;4BACvD,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI;4BAC3B,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC7B,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;wBACrC,IAAI,CAAC,UAAU,EAAE,CAAC;oBACtB,CAAC;yBAAM,CAAC;wBACJ,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;4BACtB,IAAI,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gCAC1C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;4BACvD,CAAC;iCAAM,CAAC;gCACJ,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;4BAC1C,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACJ,IAAI,CAAC,MAAM,EAAE,CAAC;wBAClB,CAAC;wBACD,IAAI,CAAC,UAAU,EAAE,CAAC;oBACtB,CAAC;gBACL,CAAC;YACL,CAAC;YACD,qBAAqB,CAAC,IAAI;gBACtB,kDAAkD;gBAClD,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrC,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBAC/F,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;oBAC9B,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;gBACD,kDAAkD;qBAC7C,IAAI,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC3F,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;oBAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;oBAC5C,IAAI,WAAmB,CAAC;oBAExB,IAAI,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC;wBAC3B,WAAW,GAAG,CAAC,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACjF,CAAC;yBAAM,IAAI,CAAC,UAAU,IAAI,SAAS,EAAE,CAAC;wBAClC,WAAW,GAAG,CAAC,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACzD,CAAC;yBAAM,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC;wBACjC,WAAW,GAAG,CAAC,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACjF,CAAC;yBAAM,CAAC;wBACJ,WAAW,GAAG,CAAC,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAClF,CAAC;oBAED,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;oBAC9B,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;YACL,CAAC;SACJ,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACK,aAAa,CAAC,IAAY;QAC9B,OAAO,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACxF,CAAC;IAED;;;;OAIG;IACK,QAAQ,CAAC,OAA2D;QACxE,OAAO,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC;YAC3F,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK;YACjB,CAAC,CAAC,IAAI,CAAC;IACf,CAAC;;AAtFL,8CAuFC;AAtF0B,4BAAU,GAA6B;IAC1D,GAAG,EAAE,mBAAmB;IACxB,gBAAgB,EAAE,IAAI;CACzB,CAAC"}