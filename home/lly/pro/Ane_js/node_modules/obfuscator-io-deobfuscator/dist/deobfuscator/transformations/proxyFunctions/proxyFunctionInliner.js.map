{"version": 3, "file": "proxyFunctionInliner.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/proxyFunctions/proxyFunctionInliner.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAA0F;AAC1F,+DAAqD;AACrD,qDAA8D;AAC9D,mDAA4G;AAC5G,6CAA8D;AAE9D,MAAa,oBAAqB,SAAQ,+BAAc;IAMpD;;;;OAIG;IACI,OAAO,CAAC,GAAgB;QAC3B,MAAM,MAAM,GAAwC,EAAE,CAAC;QACvD,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,IAAA,kBAAQ,EAAC,IAAI,CAAC,GAAG,EAAE;YACf,KAAK,CAAC,IAAI;gBACN,IAAA,kBAAW,EAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;gBACpC,MAAM,QAAQ,GAAG,IAAA,+BAAoB,EAA0B,IAAI,EAAE,yCAAyB,EAAE,IAAI,CAAC,CAAC;gBACtG,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,OAAO;gBACX,CAAC;gBAED,MAAM,aAAa,GAAG,IAAI,qCAAqB,CAAC,QAAQ,CAAC,CAAC;gBAC1D,MAAM,CAAC,IAAI,CACP,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAsC,CAAC,CAChG,CAAC;YACN,CAAC;SACJ,CAAC,CAAC;QAEH,sCAAsC;QACtC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAA,kBAAW,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,IAAA,kBAAW,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;QAC/E,KAAK,MAAM,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI,MAAM,EAAE,CAAC;YACzC,IAAI,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,IAAI,CAAC,UAAU,EAAE,CAAC;YACtB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;;AAvCL,oDAwCC;AAvC0B,+BAAU,GAA6B;IAC1D,GAAG,EAAE,uBAAuB;IAC5B,gBAAgB,EAAE,IAAI;CACzB,CAAC"}