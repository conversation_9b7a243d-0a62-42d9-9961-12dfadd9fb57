import { LogFunction, Transformation, TransformationProperties } from '../transformation';
export declare class ExpressionSimplifier extends Transformation {
    static readonly properties: TransformationProperties;
    private static readonly RESOLVABLE_UNARY_OPERATORS;
    private static readonly RESOLVABLE_BINARY_OPERATORS;
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log: LogFunction): boolean;
    /**
     * Attempts to simplify an expression.
     * @param expression The expression.
     * @returns The expression in the simplest form possible.
     */
    private simplifyExpression;
    /**
     * Attempts to simplify a unary expression.
     * @param expression The unary expression.
     * @returns The simplified expression or undefined.
     */
    private simplifyUnaryExpression;
    /**
     * Attempts to simplify a binary expression.
     * @param expression The binary expression.
     * @returns The simplified expression or undefined.
     */
    private simplifyBinaryExpression;
    /**
     * Applies a unary operation.
     * @param operator The operator.
     * @param argument The argument value.
     * @returns The resultant value.
     */
    private applyUnaryOperation;
    /**
     * Applies a binary operation.
     * @param operator The resolvable binary operator.
     * @param left The value of the left expression.
     * @param right The value of the right expression.
     * @returns The resultant value.
     */
    private applyBinaryOperation;
    /**
     * Gets the real value from a resolvable expression.
     * @param expression The resolvable expression.
     * @returns The value.
     */
    private getResolvableExpressionValue;
    /**
     * Attempts to convert a value of unknown type to an expression node.
     * @param value The value.
     * @returns The expression or undefined.
     */
    private convertValueToExpression;
    /**
     * Returns whether a node is a resolvable expression that can be
     * evaluated safely.
     * @param node The AST node.
     * @returns Whether.
     */
    private isResolvableExpression;
}
