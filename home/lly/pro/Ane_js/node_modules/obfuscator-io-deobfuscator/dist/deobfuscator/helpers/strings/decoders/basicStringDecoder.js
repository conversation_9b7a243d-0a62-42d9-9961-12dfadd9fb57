"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicStringDecoder = void 0;
const stringDecoder_1 = require("./stringDecoder");
class BasicStringDecoder extends stringDecoder_1.StringDecoder {
    /**
     * Returns the type of the decoder.
     */
    get type() {
        return stringDecoder_1.DecoderType.BASIC;
    }
    /**
     * Decodes a string.
     * @param index The index.
     */
    getString(index) {
        return this.stringArray[index + this.indexOffset];
    }
    /**
     * Decodes a string for the rotate string call.
     * @param index The index.
     * @returns THe string.
     */
    getStringForRotation(index) {
        return this.getString(index);
    }
}
exports.BasicStringDecoder = BasicStringDecoder;
//# sourceMappingURL=basicStringDecoder.js.map