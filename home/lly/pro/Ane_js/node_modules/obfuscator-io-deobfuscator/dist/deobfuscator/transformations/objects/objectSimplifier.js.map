{"version": 3, "file": "objectSimplifier.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/objects/objectSimplifier.ts"], "names": [], "mappings": ";;;;;;AACA,sDAAgH;AAChH,+DAAqD;AACrD,qDAA8D;AAC9D,+CAA4F;AAC5F,6CAA8D;AAM9D,MAAa,gBAAiB,SAAQ,+BAAc;IAOhD;;;;OAIG;IACH,YAAY,GAAW,EAAE,MAAkC;QACvD,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,GAAgB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,MAAM,MAAM,GAA8B,EAAE,CAAC;QAC7C,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,IAAA,kBAAQ,EAAC,IAAI,CAAC,GAAG,EAAE;YACf,KAAK,CAAC,IAAI;gBACN,IAAA,kBAAW,EAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;gBACpC,MAAM,QAAQ,GAAG,IAAA,+BAAoB,EAAwB,IAAI,EAAE,qCAAuB,CAAC,CAAC;gBAC5F,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,OAAO;gBACX,CAAC;gBAED,sCAAsC;gBACtC,KAAK,MAAM,aAAa,IAAI,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;oBAC1D,IACI,aAAa,CAAC,UAAU;wBACxB,aAAa,CAAC,UAAU,CAAC,kBAAkB,EAAE;wBAC7C,aAAa,CAAC,UAAU,CAAC,UAAU;wBACnC,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,sBAAsB,EAAE;wBAC5D,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI,MAAM,EACxC,CAAC;wBACC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;4BAC7B,GAAG,CAAC,wBAAwB,QAAQ,CAAC,IAAI,oBAAoB,CAAC,CAAC;4BAC/D,IAAI,CAAC,IAAI,EAAE,CAAC;4BACZ,OAAO;wBACX,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,MAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,QAAQ,CAAC,CAAC;gBAC9C,WAAW,CAAC,OAAO,EAAE,CAAC;gBAEtB,MAAM,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAA4B,CAAC,CAAC,CAAC;YAClG,CAAC;SACJ,CAAC,CAAC;QAEH,iCAAiC;QACjC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAA,kBAAW,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,IAAA,kBAAW,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;QAC/E,KAAK,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,MAAM,EAAE,CAAC;YACvC,IAAI,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,IAAI,CAAC,UAAU,EAAE,CAAC;YACtB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;;AApEL,4CAqEC;AApE0B,2BAAU,GAA6B;IAC1D,GAAG,EAAE,sBAAsB;IAC3B,gBAAgB,EAAE,IAAI;CACzB,CAAC"}