{"version": 3, "file": "proxyFunction.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/proxyFunctions/proxyFunction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAElC,6CAAoD;AACpD,+DAAqD;AAWrD;;;;GAIG;AACI,MAAM,yBAAyB,GAAG,CAAC,IAAY,EAAmC,EAAE;IACvF,OAAO,CACH,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;YAC1B,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,SAAS;gBACpC,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;oBACvC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC;gBAC9B,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;gBACzB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CACpC,CAAC;AACN,CAAC,CAAC;AAdW,QAAA,yBAAyB,6BAcpC;AAEF;;;;GAIG;AACH,MAAM,YAAY,GAAG,CAAC,IAAY,EAAW,EAAE;IAC3C,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;QACjF,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,IAAI,OAAO,GAAG,IAAI,CAAC;IAEnB,IAAA,kBAAQ,EAAC,IAAI,EAAE;QACX,CAAC,iEAAiE,CAAC,CAAC,IAAI;YACpE,OAAO,GAAG,KAAK,CAAC;YAChB,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;QACD,OAAO,EAAE,IAAI;KAChB,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAIF,MAAa,aAAa;IAGtB;;;OAGG;IACH,YAAY,UAAmC;QAC3C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,IAAgB;QAClC,MAAM,UAAU,GAAG,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACnD,CAAC,CAAC,IAAA,qBAAc,EAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACtC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ;gBACvC,CAAC,CAAC,IAAA,qBAAc,EAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;gBACvD,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAChC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACzC,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACK,iBAAiB,CAAC,UAAwB,EAAE,IAAgB;QAChE,MAAM,QAAQ,GAAG,IAAI,GAAG,CACpB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAmB,EAAE,KAAa,EAAE,EAAE,CAAC;YAC/D,KAAK,CAAC,IAAI;YACV,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC;SAC3C,CAAC,CACL,CAAC;QACF,MAAM,cAAc,GAA+B,EAAE,CAAC;QAEtD,IAAA,kBAAQ,EAAC,UAAU,EAAE;YACjB,KAAK,CAAC,IAAI;gBACN,IACI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;oBACzB,gCAAgC;oBAChC,CAAC,CACG,IAAI,CAAC,UAAU;wBACf,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE;wBACpC,IAAI,CAAC,GAAG,IAAI,UAAU,CACzB;oBACD,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9B,CAAC;oBACC,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAiB,CAAC;oBACjE,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;gBAC7C,CAAC;YACL,CAAC;YACD,OAAO,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,KAAK,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,cAAc,EAAE,CAAC;YAC/C,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;CACJ;AA/DD,sCA+DC;AAED,MAAa,qBAAsB,SAAQ,aAAa;IAGpD;;;OAGG;IACH,YAAY,QAAmD;QAC3D,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC;IAChD,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,IAAc;QAC7B,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,QAAQ,EAAE,CAAC;YAChF,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;QAChB,CAAC;aAAM,CAAC;YACJ,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;CACJ;AAlCD,sDAkCC"}