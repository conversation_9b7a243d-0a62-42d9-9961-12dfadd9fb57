import { LogFunction, Transformation, TransformationProperties } from '../transformation';
export declare class StringRevealer extends Transformation {
    static readonly properties: TransformationProperties;
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log: LogFunction): boolean;
    /**
     * Returns whether a node is directly declaring a string array.
     * @param node The AST node.
     * @returns Whether.
     */
    private isDirectStringArrayDeclarator;
    /**
     * Returns whether a node is the function that splits and returns the
     * string array.
     * @param node The AST node.
     * @returns Whether.
     */
    private isStringArrayFunction;
    /**
     * Returns whether a node is a basic string array wrapper function.
     * @param node The AST node.
     * @param stringArrayName The name of the string array function.
     * @returns Whether.
     */
    private isBasicStringArrayWrapper;
    /**
     * Returns whether a node is either a base 64 or RC4 string array wrapper function.
     * @param node The AST node.
     * @param stringArrayName The name of the string array function.
     * @returns Whether.
     */
    private isComplexStringArrayWrapper;
    /**
     * Returns whether a node is either a base 64 or RC4 string array wrapper function,
     * around a direct string array.
     * @param node The AST node.
     * @param stringArrayName The name of the string array.
     * @returns Whether.
     */
    private isComplexDirectStringArrayWrapper;
    /**
     * Returns whether a node is a call to rotate the string array.
     * @param node The AST node.
     * @param stringArrayName The name of the string array function.
     * @returns Whether.
     */
    private isRotateStringArrayCall;
    /**
     * Returns whether a node is a call of the string array wrapper function.
     * @param node The AST node.
     * @param wrapperType The type of string wrapper.
     * @returns Whether.
     */
    private isStringArrayWrapperCall;
    /**
     * Returns whether a node is an escaped string literal.
     * @param node The AST node.
     * @returns Whether.
     */
    private isEscapedStringLiteral;
}
