import { LogFunction, Transformation, TransformationProperties } from '../transformation';
export declare class SequenceSplitter extends Transformation {
    static readonly properties: TransformationProperties;
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log: LogFunction): boolean;
    /**
     * Returns whether a node that is the last in a sequence expression
     * is excluded from being placed on its own.
     * @param node The AST node.
     * @returns Whether.
     */
    private isExcluded;
}
