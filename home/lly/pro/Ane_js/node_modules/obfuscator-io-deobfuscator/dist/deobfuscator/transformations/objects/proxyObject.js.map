{"version": 3, "file": "proxyObject.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/objects/proxyObject.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAElC,6CAAoD;AACpD,mEAA2F;AAK3F;;;;GAIG;AACI,MAAM,uBAAuB,GAAG,CAAC,IAAY,EAAiC,EAAE;IACnF,OAAO,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AACpE,CAAC,CAAC;AAFW,QAAA,uBAAuB,2BAElC;AAEF,MAAa,WAAW;IAKpB;;;OAGG;IACH,YAAY,QAAiD;QAP5C,sBAAiB,GAAuC,IAAI,GAAG,EAAE,CAAC;QAClE,4BAAuB,GAAwC,IAAI,GAAG,EAAE,CAAC;QAOtF,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YACzD,IAAI,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtE,MAAM,GAAG,GAAG,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC;gBAClF,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACpD,CAAC;qBAAM,IAAI,IAAA,yCAAyB,EAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBACnD,MAAM,aAAa,GAAG,IAAI,6BAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACxD,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;gBACzD,CAAC;YACL,CAAC;iBAAM,IAAI,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzE,MAAM,GAAG,GAAG,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC;gBAClF,IAAI,IAAA,yCAAyB,EAAC,QAAQ,CAAC,EAAE,CAAC;oBACtC,MAAM,aAAa,GAAG,IAAI,6BAAa,CAAC,QAAQ,CAAC,CAAC;oBAClD,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;gBACzD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC;IAChD,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,IAAc;QAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IACI,UAAU;YACV,UAAU,CAAC,kBAAkB,EAAE;YAC/B,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC;YACxC,CAAC,CAAC,UAAU,CAAC,UAAU;gBACnB,CAAC,UAAU,CAAC,UAAU,CAAC,sBAAsB,EAAE;gBAC/C,UAAU,CAAC,SAAS,IAAI,MAAM,CAAC,EACrC,CAAC;YACC,MAAM,GAAG,GAAG,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAChD,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI;gBAC/B,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YAErC,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAiB,CAAC;gBAC9D,UAAU,CAAC,WAAW,CAAC,IAAA,qBAAc,EAAC,KAAK,CAAC,CAAC,CAAC;gBAC9C,OAAO,IAAI,CAAC;YAChB,CAAC;iBAAM,IACH,UAAU,CAAC,UAAU;gBACrB,UAAU,CAAC,UAAU,CAAC,gBAAgB,EAAE;gBACxC,UAAU,CAAC,GAAG,IAAI,QAAQ;gBAC1B,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,EACvC,CAAC;gBACC,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAkB,CAAC;gBAC7E,MAAM,WAAW,GAAG,aAAa,CAAC,cAAc,CAC5C,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CACvC,CAAC;gBACF,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBAC/C,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACK,oBAAoB,CACxB,QAA0B;QAI1B,OAAO,CACH,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC;YAC/B,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC;YAChC,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CACvD,CAAC;IACN,CAAC;IAED;;;;OAIG;IACK,kBAAkB,CACtB,QAAwB;QAIxB,OAAO,CACH,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC;YAC/B,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC;YAChC,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CACvD,CAAC;IACN,CAAC;IAED;;;;OAIG;IACK,kBAAkB,CACtB,MAA0B;QAI1B,OAAO,CACH,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC;YAClC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC;YACnC,CAAC,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CACxD,CAAC;IACN,CAAC;CACJ;AAvID,kCAuIC"}