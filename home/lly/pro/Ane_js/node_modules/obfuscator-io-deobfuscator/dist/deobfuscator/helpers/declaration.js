"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.isDeclarationOrAssignmentStatement = isDeclarationOrAssignmentStatement;
exports.isDeclarationOrAssignmentExpression = isDeclarationOrAssignmentExpression;
const t = __importStar(require("@babel/types"));
/**
 * Checks whether a node is a variable declaration or assignment expression
 * within an expression statement that is initialising a variable that
 * satisfies the provided constraints.
 * @param node The AST node.
 * @param isId The function that determines whether the variable being declared matches.
 * @param isValue The function that determines whether the value the variable is initialised to matches.
 * @returns Whether.
 */
function isDeclarationOrAssignmentStatement(node, isId, isValue) {
    return ((t.isVariableDeclaration(node) &&
        node.declarations.length == 1 &&
        isId(node.declarations[0].id) &&
        node.declarations[0].init &&
        isValue(node.declarations[0].init)) ||
        (t.isExpressionStatement(node) &&
            t.isAssignmentExpression(node.expression) &&
            isId(node.expression.left) &&
            isValue(node.expression.right)));
}
function isDeclarationOrAssignmentExpression(node, isId, isValue) {
    return ((t.isVariableDeclaration(node) &&
        node.declarations.length == 1 &&
        isId(node.declarations[0].id) &&
        node.declarations[0].init &&
        isValue(node.declarations[0].init)) ||
        (t.isAssignmentExpression(node) && isId(node.left) && isValue(node.right)));
}
//# sourceMappingURL=declaration.js.map