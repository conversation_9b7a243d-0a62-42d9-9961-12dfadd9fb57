import * as t from '@babel/types';
import { Config } from './transformations/config';
export declare class Deobfuscator {
    private readonly ast;
    private readonly config;
    private readonly transformationTypes;
    private static readonly MAX_ITERATIONS;
    /**
     * Creates a new deobfuscator.
     * @param ast The AST.
     * @param config The config (optional).
     */
    constructor(ast: t.File, config?: Config);
    /**
     * Executes the deobfuscator.
     * @returns The simplified code.
     */
    execute(): string;
    /**
     * Clears the traversal cache to force the scoping to be handled
     * again on the next traverse.
     */
    private clearCache;
}
