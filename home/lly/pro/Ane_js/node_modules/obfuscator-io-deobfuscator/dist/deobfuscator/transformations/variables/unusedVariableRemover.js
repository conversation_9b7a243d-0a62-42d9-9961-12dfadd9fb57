"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnusedVariableRemover = void 0;
const t = __importStar(require("@babel/types"));
const traverse_1 = __importDefault(require("@babel/traverse"));
const transformation_1 = require("../transformation");
class UnusedVariableRemover extends transformation_1.Transformation {
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log) {
        const self = this;
        (0, traverse_1.default)(this.ast, {
            Scope(path) {
                for (const binding of Object.values(path.scope.bindings)) {
                    if (!binding.referenced &&
                        binding.constantViolations.length == 0 &&
                        binding.path.key != 'handler' &&
                        !binding.path.isFunctionExpression() // don't remove named function expressions
                    ) {
                        // ensure we don't remove variables that are exposed globally
                        if (t.isProgram(binding.scope.block) &&
                            (binding.kind == 'var' || binding.kind == 'hoisted')) {
                            return;
                        }
                        const paths = binding.path.parentKey == 'params'
                            ? [...binding.referencePaths, ...binding.constantViolations]
                            : [
                                binding.path,
                                ...binding.referencePaths,
                                ...binding.constantViolations
                            ];
                        for (const path of paths) {
                            // skip any patterns declaring other variables
                            if (path.isVariableDeclarator() &&
                                ((t.isArrayPattern(path.node.id) &&
                                    path.node.id.elements.length > 1) ||
                                    (t.isObjectPattern(path.node.id) &&
                                        path.node.id.properties.length > 1))) {
                                continue;
                            }
                            if (path.key == 'consequent' ||
                                path.key == 'alternate' ||
                                path.key == 'body') {
                                path.replaceWith(t.blockStatement([]));
                            }
                            else {
                                // check if we are going to create an empty variable declaration (otherwise can sometimes trigger Babel build error)
                                const parentPath = path.parentPath;
                                if (parentPath &&
                                    parentPath.isVariableDeclaration() &&
                                    parentPath.node.declarations.length == 1) {
                                    parentPath.remove();
                                }
                                else {
                                    path.remove();
                                }
                            }
                            if (paths.length > 0) {
                                self.setChanged();
                            }
                        }
                    }
                }
            }
        });
        return this.hasChanged();
    }
}
exports.UnusedVariableRemover = UnusedVariableRemover;
UnusedVariableRemover.properties = {
    key: 'unusedVariableRemoval',
    rebuildScopeTree: true
};
//# sourceMappingURL=unusedVariableRemover.js.map