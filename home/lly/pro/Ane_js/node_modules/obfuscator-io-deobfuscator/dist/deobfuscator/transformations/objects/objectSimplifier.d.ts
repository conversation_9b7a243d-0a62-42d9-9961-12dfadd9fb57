import * as t from '@babel/types';
import { LogFunction, Transformation, TransformationConfig, TransformationProperties } from '../transformation';
interface ObjectSimplificationConfig extends TransformationConfig {
    unsafeReplace?: boolean;
}
export declare class ObjectSimplifier extends Transformation {
    static readonly properties: TransformationProperties;
    private readonly config;
    /**
     * Creates a new transformation.
     * @param ast The AST.
     * @param config The config.
     */
    constructor(ast: t.File, config: ObjectSimplificationConfig);
    /**
     * Executes the transformation.
     * @param log The log function.
     * @returns Whether any changes were made.
     */
    execute(log: LogFunction): boolean;
}
export {};
