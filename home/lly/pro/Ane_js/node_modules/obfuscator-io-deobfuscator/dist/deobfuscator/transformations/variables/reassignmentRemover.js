"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReassignmentRemover = void 0;
const t = __importStar(require("@babel/types"));
const transformation_1 = require("../transformation");
const variable_1 = require("../../helpers/variable");
const traverse_1 = __importDefault(require("@babel/traverse"));
class ReassignmentRemover extends transformation_1.Transformation {
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log) {
        const self = this;
        (0, traverse_1.default)(this.ast, {
            enter(path) {
                const variable = (0, variable_1.findConstantVariable)(path, t.isIdentifier);
                if (!variable || variable.name == variable.expression.name) {
                    return;
                }
                // check that the variable we would replace with isn't reassigned multiple times
                const assignedBinding = path.scope.getBinding(variable.expression.name);
                if (assignedBinding &&
                    !assignedBinding.constant &&
                    !((assignedBinding.constantViolations.length == 1 &&
                        assignedBinding.path.isVariableDeclarator() &&
                        assignedBinding.path.node.init == undefined) ||
                        self.isExcludedConstantViolation(assignedBinding))) {
                    return;
                }
                for (const referencePath of variable.binding.referencePaths) {
                    referencePath.replaceWith(t.identifier(variable.expression.name));
                    self.setChanged();
                }
                // remove any declarations of variable we are replacing
                for (const declarationPath of [
                    ...variable.binding.constantViolations,
                    variable.binding.path
                ]) {
                    if (declarationPath != path) {
                        declarationPath.remove();
                    }
                }
                if (path.isStatement() ||
                    path.isVariableDeclarator() ||
                    (path.parentPath &&
                        (path.parentPath.isStatement() ||
                            (path.parentPath.isSequenceExpression() &&
                                path.node !=
                                    path.parentPath.node.expressions[path.parentPath.node.expressions.length - 1])))) {
                    path.remove();
                }
                else {
                    // might have side effects, replace with RHS instead
                    path.replaceWith(variable.expression);
                }
            }
        });
        return this.hasChanged();
    }
    /**
     * Checks whether a binding has a constant violation that reassigns a function from
     * within (i.e. string decoder function), and thus should be treated as constant.
     * @param assignedBinding The binding.
     * @returns Whether.
     */
    isExcludedConstantViolation(assignedBinding) {
        if (assignedBinding.constantViolations.length == 1 &&
            assignedBinding.path.isFunctionDeclaration()) {
            const functionParent = assignedBinding.constantViolations[0].getFunctionParent();
            return functionParent && functionParent.node == assignedBinding.path.node;
        }
        else {
            return false;
        }
    }
}
exports.ReassignmentRemover = ReassignmentRemover;
ReassignmentRemover.properties = {
    key: 'reassignmentRemoval',
    rebuildScopeTree: true
};
//# sourceMappingURL=reassignmentRemover.js.map