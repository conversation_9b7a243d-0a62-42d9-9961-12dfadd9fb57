import { TransformationConfig } from './transformation';
export type TransformationKey = 'objectSimplification' | 'objectPacking' | 'proxyFunctionInlining' | 'stringRevealing' | 'expressionSimplification' | 'constantPropagation' | 'reassignmentRemoval' | 'sequenceSplitting' | 'controlFlowRecovery' | 'deadBranchRemoval' | 'antiTamperRemoval' | 'unusedVariableRemoval' | 'propertySimplification';
export type Config = {
    [key in TransformationKey]: TransformationConfig;
} & {
    silent?: boolean;
};
export declare const defaultConfig: Config;
