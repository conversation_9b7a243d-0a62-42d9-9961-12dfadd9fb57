import * as t from '@babel/types';
import { StringDecoder } from '../decoders/stringDecoder';
/**
 * Rotates the string array.
 * @param expression The expression containing the string array calls.
 * @param decoderMap The string decoder map.
 * @param stopValue The value to stop at.
 */
export declare function rotateStringArray(array: string[], expression: t.BinaryExpression, decoderMap: Map<string, StringDecoder>, stopValue: number): void;
