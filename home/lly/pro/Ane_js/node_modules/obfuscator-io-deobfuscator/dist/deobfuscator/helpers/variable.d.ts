import { Binding, NodePath } from '@babel/traverse';
import * as t from '@babel/types';
export declare abstract class ConstantVariable<T extends t.Node> {
    readonly name: string;
    readonly binding: Binding;
    readonly expression: T;
    /**
     * Creates a new constant variable.
     * @param name The name of the variable.
     * @param binding The binding.
     * @param expression The value the variable holds.
     */
    constructor(name: string, binding: Binding, expression: T);
    /**
     * Removes the variable and any declarations.
     */
    abstract remove(): void;
}
export declare class ConstantDeclarationVariable<T extends t.Node> extends ConstantVariable<T> {
    private readonly declaratorPath;
    /**
     * Creates a new constant variable that is declared and initialised immediately.
     * @param declaratorPath The path of the variable declarator.
     * @param name The name of the variable.
     * @param binding The binding.
     * @param expression The value the variable holds.
     */
    constructor(declaratorPath: NodePath<t.Node>, name: string, binding: Binding, expression: T);
    /**
     * Removes the variable.
     */
    remove(): void;
}
export declare class ConstantAssignmentVariable<T extends t.Node> extends ConstantVariable<T> {
    private readonly declaratorPath;
    private readonly assignmentPath;
    /**
     * Creates a new constant variable that is declared with no value then assigned to later.
     * @param declaratorPath The path of the variable declarator.
     * @param assignmentPath The path of the assignment to the variable.
     * @param name The name of the variable.
     * @param binding The binding.
     * @param expression The value the variable holds.
     */
    constructor(declaratorPath: NodePath<t.Node>, assignmentPath: NodePath<t.AssignmentExpression>, name: string, binding: Binding, expression: T);
    /**
     * Removes the variable.
     */
    remove(): void;
}
export type isTypeFunction<T extends t.Node> = (node: t.Node) => node is T;
/**
 * Checks whether a node is initialising a 'constant' variable and returns the variable if so.
 * @param path The path.
 * @param isType The function that determines whether the expression is of the desired type.
 * @returns The constant variable or undefined.
 */
export declare function findConstantVariable<T extends t.Node>(path: NodePath, isType: isTypeFunction<T>, canBeFunction?: boolean): ConstantVariable<T> | undefined;
