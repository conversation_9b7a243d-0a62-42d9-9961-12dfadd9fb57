{"version": 3, "file": "objectPacker.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/objects/objectPacker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,qDAA8D;AAC9D,sDAA0F;AAC1F,+DAAqD;AAErD,MAAa,YAAa,SAAQ,+BAAc;IAK5C;;;OAGG;IACI,OAAO,CAAC,GAAgB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAA,kBAAQ,EAAC,IAAI,CAAC,GAAG,EAAE;YACf,KAAK,CAAC,IAAI;gBACN,MAAM,QAAQ,GAAG,IAAA,+BAAoB,EACjC,IAAI,EACJ,uBAAuB,CAC1B,CAAC;gBACF,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,OAAO;gBACX,CAAC;gBAED,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAChD,IACI,CAAC,aAAa;oBACd,aAAa,CAAC,UAAU,IAAI,SAAS;oBACrC,OAAO,aAAa,CAAC,GAAG,IAAI,QAAQ,EACtC,CAAC;oBACC,OAAO;gBACX,CAAC;gBAED,MAAM,UAAU,GAAI,aAAa,CAAC,UAAU,CAAC,IAAY,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBACnF,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gBAClE,IAAI,UAAU,GAAG,CAAC,CAAC;gBACnB,KAAK,IAAI,CAAC,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC7D,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;oBAC3B,IACI,CAAC,CAAC,qBAAqB,CAAC,IAAI,CAAC;wBAC7B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,EAC3D,CAAC;wBACC,yDAAyD;wBACzD,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;4BAClE,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;4BAC1C,IAAI,KAAK,GAAiB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;4BAChD,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gCACrD,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gCAC5B,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;4BACxB,CAAC;4BAED,gDAAgD;4BAChD,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gCACtB,MAAM;4BACV,CAAC;4BAED,KAAK,MAAM,EAAE,QAAQ,EAAE,IAAI,UAAU,EAAE,CAAC;gCACpC,MAAM,UAAU,GACZ,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC;oCAC5B,CAAC,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC;oCAC7B,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gCAC9B,MAAM,cAAc,GAAG,CAAC,CAAC,cAAc,CACnC,QAAQ,EACR,KAAK,EACL,UAAU,CACb,CAAC;gCACF,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gCACpD,IAAI,CAAC,UAAU,EAAE,CAAC;gCAClB,UAAU,EAAE,CAAC;4BACjB,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACJ,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;4BAC1C,MAAM,UAAU,GACZ,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC;gCACvB,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC;gCACxB,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;4BAEzB,8EAA8E;4BAC9E,IACI,IAAI,CAAC,gBAAgB,CACjB,IAAI,CAAC,UAAU,CAAC,KAAK,EACrB,aAAa,EACb,CAAC,EACD,gBAAgB,EAChB,GAAG,CACN,EACH,CAAC;gCACC,MAAM;4BACV,CAAC;4BAED,MAAM,QAAQ,GAAG,CAAC,CAAC,cAAc,CAC7B,GAAG,EACH,IAAI,CAAC,UAAU,CAAC,KAAK,EACrB,UAAU,CACb,CAAC;4BACF,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;4BAC9C,IAAI,CAAC,UAAU,EAAE,CAAC;4BAClB,UAAU,EAAE,CAAC;wBACjB,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,MAAM;oBACV,CAAC;gBACL,CAAC;gBAED,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;YACzD,CAAC;SACJ,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;;;OAQG;IACK,gBAAgB,CACpB,KAAa,EACb,aAAuB,EACvB,UAAkB,EAClB,gBAA+B,EAC/B,GAAgB;QAEhB,IAAI,CAAC;YACD,MAAM,SAAS,GAAG,aAAa,CAAC,UAAW,CAAC,GAAG,CAC3C,GAAG,aAAa,CAAC,SAAS,IAAI,UAAU,EAAE,CACjC,CAAC;YACd,IAAI,gBAAgB,GAAG,KAAK,CAAC;YAE7B,IAAA,kBAAQ,EACJ,KAAK,EACL;gBACI,UAAU,CAAC,IAAI;oBACX,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC7B,gBAAgB,GAAG,IAAI,CAAC;oBAC5B,CAAC;gBACL,CAAC;aACJ,EACD,SAAS,CAAC,KAAK,EACf,SAAS,EACT,SAAS,CACZ,CAAC;YAEF,OAAO,gBAAgB,CAAC;QAC5B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,GAAG,CAAC,yDAAyD,GAAG,EAAE,CAAC,CAAC;YACpE,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,oBAAoB,CACxB,IAAY,EACZ,UAAkB;QAElB,OAAO,CACH,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC;YAC9B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;YAC/B,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,UAAU,CACtC,CAAC;IACN,CAAC;;AAxKL,oCAyKC;AAxK0B,uBAAU,GAA6B;IAC1D,GAAG,EAAE,eAAe;CACvB,CAAC;AA0KN;;;;GAIG;AACH,MAAM,uBAAuB,GAAG,CAAC,IAAY,EAAiC,EAAE;IAC5E,OAAO,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC;AACrE,CAAC,CAAC"}