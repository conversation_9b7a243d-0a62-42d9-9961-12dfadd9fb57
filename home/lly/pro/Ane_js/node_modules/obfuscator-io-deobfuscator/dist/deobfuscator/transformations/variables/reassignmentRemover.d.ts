import { LogFunction, Transformation, TransformationProperties } from '../transformation';
export declare class ReassignmentRemover extends Transformation {
    static readonly properties: TransformationProperties;
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log: LogFunction): boolean;
    /**
     * Checks whether a binding has a constant violation that reassigns a function from
     * within (i.e. string decoder function), and thus should be treated as constant.
     * @param assignedBinding The binding.
     * @returns Whether.
     */
    private isExcludedConstantViolation;
}
