{"version": 3, "file": "controlFlowRecoverer.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/controlFlow/controlFlowRecoverer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,+DAAuC;AACvC,sDAA0F;AAC1F,qDAA8D;AAC9D,2DAAmH;AAEnH,MAAa,oBAAqB,SAAQ,+BAAc;IAMpD;;;OAGG;IACI,OAAO,CAAC,GAAgB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAA,kBAAQ,EAAC,IAAI,CAAC,GAAG,EAAE;YACf,KAAK,CAAC,IAAI;gBACN,MAAM,aAAa,GAAG,IAAA,+BAAoB,EAAuB,IAAI,EAAE,sBAAsB,CAAC,CAAC;gBAC/F,IAAI,CAAC,aAAa,EAAE,CAAC;oBACjB,OAAO;gBACX,CAAC;gBAED,MAAM,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAC7D,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAC9C,CAAC;gBAEF,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAChD,IAAI,CAAC,aAAa,EAAE,CAAC;oBACjB,OAAO;gBACX,CAAC;gBAED,IAAI,QAAQ,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;gBAC9C,IAAI,YAAoB,CAAC;gBACzB,IAAI,mBAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;oBACzD,YAAY,GAAG,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;wBACvD,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;wBAChC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;gBACxD,CAAC;qBAAM,IAAI,IAAA,iDAAmC,EAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC;oBAChG,MAAM,WAAW,GAAG,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC;wBACvD,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;wBACzB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;oBAC5C,YAAY,GAAG,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAClD,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;wBAC3B,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC/C,QAAQ,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;oBAErC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,CAAC;wBACzE,OAAO;oBACX,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,OAAO;gBACX,CAAC;gBAED,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAC/C,MAAM,QAAQ,GAAG,IAAI,GAAG,CACpB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAE,CAAC,CAAC,IAAwB,CAAC,KAAK,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CACpE,CAAC;gBAEF,MAAM,UAAU,GAAG,EAAE,CAAC;gBACtB,KAAK,IAAI,CAAC,GAAG,YAAY,GAAI,CAAC,EAAE,EAAE,CAAC;oBAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;wBACvB,MAAM;oBACV,CAAC;oBAED,MAAM,eAAe,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAkB,CAAC;oBAC7D,UAAU,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3E,IACI,eAAe,CAAC,MAAM,GAAG,CAAC;wBAC1B,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAClE,CAAC;wBACC,MAAM;oBACV,CAAC;gBACL,CAAC;gBAED,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,QAAQ,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;gBACzC,IAAI,CAAC,UAAU,EAAE,CAAC;YACtB,CAAC;SACJ,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;;AAhFL,oDAiFC;AAhF0B,+BAAU,GAA6B;IAC1D,GAAG,EAAE,qBAAqB;IAC1B,gBAAgB,EAAE,IAAI;CACzB,CAAC;AAoFN;;;;GAIG;AACH,MAAM,sBAAsB,GAAG,CAAC,IAAY,EAAgC,EAAE;IAC1E,OAAO,CACH,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACxB,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC;QACjC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC;YAC/E,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC;QACnF,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;QAC1B,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CACvC,CAAC;AACN,CAAC,CAAC;AAeF;;;;;;GAMG;AACH,MAAM,oBAAoB,GAAG,CAAC,IAAY,EAAE,UAAkB,EAAE,WAAmB,EAA8B,EAAE;IAC/G,OAAO,CACH,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;QACrB,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;QAC/C,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC;QAChD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,IAAI,UAAU;QACnD,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC;QACxD,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC3D,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,WAAW;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CACrE,CAAC;AACN,CAAC,CAAC;AAOF;;;;;GAKG;AACH,MAAM,mBAAmB,GAAG,CAAC,IAAY,EAAE,UAAkB,EAA6B,EAAE;IACxF,OAAO,CACH,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,IAAI,IAAI,SAAS;QACtB,IAAA,iDAAmC,EAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,gBAAgB,CAAC;QAClF,oBAAoB,CAChB,IAAI,CAAC,IAAI,EACT,UAAU,EACV,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAChG,CACJ,CAAC;AACN,CAAC,CAAC;AAOF;;;;;GAKG;AACH,MAAM,qBAAqB,GAAG,CAAC,IAAY,EAAE,UAAkB,EAAE,WAAmB,EAA+B,EAAE;IACjH,OAAO,CACH,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACxB,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI;QACvB,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC,CAC3D,CAAC;AACN,CAAC,CAAC"}