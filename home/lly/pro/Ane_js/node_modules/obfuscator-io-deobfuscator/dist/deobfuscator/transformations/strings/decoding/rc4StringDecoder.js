"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Rc4StringDecoder = void 0;
const common_1 = require("./common");
const stringDecoder_1 = require("./stringDecoder");
class Rc4StringDecoder extends stringDecoder_1.StringDecoder {
    /**
     * Creates a new RC4 string decoder.
     * @param stringArray The string array.
     * @param indexOffset The offset used when accessing elements by index.
     */
    constructor(stringArray, indexOffset) {
        super(stringArray, indexOffset);
        this.stringCache = new Map();
        this.isFirstCall = true;
    }
    /**
     * Decodes a string.
     * @param index The index.
     */
    getString(index, key) {
        if (this.isFirstCall) {
            this.isFirstCall = false;
            throw new Error();
        }
        const cacheKey = index + this.stringArray[0];
        if (this.stringCache.has(cacheKey)) {
            return this.stringCache.get(cacheKey);
        }
        const encoded = this.stringArray[index + this.indexOffset];
        const str = this.rc4Decode(encoded, key);
        this.stringCache.set(cacheKey, str);
        return str;
    }
    /**
     * Decodes a string encoded with RC4.
     * @param str The RC4 encoded string.
     * @param key The key.
     * @returns The decoded string.
     */
    rc4Decode(str, key) {
        const s = [];
        let j = 0;
        let decoded = '';
        str = (0, common_1.base64Transform)(str);
        for (var i = 0; i < 256; i++) {
            s[i] = i;
        }
        for (var i = 0; i < 256; i++) {
            j = (j + s[i] + key.charCodeAt(i % key.length)) % 256;
            [s[i], s[j]] = [s[j], s[i]];
        }
        i = 0;
        j = 0;
        for (let y = 0; y < str.length; y++) {
            i = (i + 1) % 256;
            j = (j + s[i]) % 256;
            [s[i], s[j]] = [s[j], s[i]];
            decoded += String.fromCharCode(str.charCodeAt(y) ^ s[(s[i] + s[j]) % 256]);
        }
        return decoded;
    }
}
exports.Rc4StringDecoder = Rc4StringDecoder;
const stringArray = [
    'WRXEc8kDW64',
    'WRmZW4qjEq',
    'amoecZvv',
    'WPL9W4NdVIG',
    'WOLvgMyo',
    'W44gWPevFMVcGfzN',
    'EvddIHOX',
    'WRKfxSkCW5vVjhWxWOldQuD8',
    'W74DbINcKa',
    'W6xcLmknWQ1x',
    'Fh9cfse',
    'W5f2WR53Ea',
    'WPbfESkVWRW',
    'WQbKW7BdPxC',
    'WPjVo1yg',
    'WRJdQGldMMy',
    'FdJcRGtcKW',
    'WPTtW69bia',
    'BCkgxHS',
    'tSkcW5mZaa',
    'W4RcQH9CdfZcSay0WOZcHNldOq',
    'D3LwiXS',
    'W6qietldVW',
    'W7ldJZbqja',
    'fmkeWQeRW5C',
    'gmoniZO',
    'WPTIW7b7hG',
    'WPZcJSoVW4L6',
    'WOXTphuG',
    'ugaXtXq',
    'WRXWW6ZdUZy',
    'WPDfW6vCfq',
    'WPRcRSoKW6HC',
    'W7y2WQ7cT3q',
    'pCoderHX',
    'W4RcISomb8k5',
    'y1njocq',
    'W5fDWOXbvW',
    'BJ7cKaRcJG',
    'WPRdKZddHLa',
    'WRT7W4VdQKi',
    'ACoJcmolWPK',
    'WQK2W4jsdW',
    'BxC4xHG',
    'WO1DDSkSW5W',
    'W6VdUIrQdW',
    'BHRcIrlcQq',
    'WP1AW4m4',
    'WRDpAu7dOq',
    'W7adWPlcKM0',
    'sSksaG52',
    'W6xcTmkVWO5m',
    'W6FdIZP2ka',
    'WO3cHCorW5Lh',
    'WOq1aKWx',
    'vaNcJa3cNa',
    'WPlcOmkrW6Dx',
    'W7njrmohWO0',
    'WQv5W4/dUhO',
    'qCoyl8ol',
    'pb/cHSk4hq',
    'W700bq7cJW',
    'nCovpqXX',
    'W6dcM8kAw00',
    'z3avtcC',
    'fc58l24',
    'W645WOZcGxS',
    'hSoACYTR',
    'WOVdIg4Yyq',
    'rIdcIGRcJa',
    'WOHfggac',
    'tSkpWR0owW',
    'W7KnW7RdOSk5',
    'W4ldQYjpeW',
    'WQfmW5ddVhy',
    'W7xdPCkJqKq',
    'W5KIW5ldQ8ko',
    'WQddHbpdNva',
    'rSkyyCouWPu',
    'v2xcIHT+',
    'brBcS8kTja',
    'tINcRtpcIW',
    'EYZcONLl',
    'W483WRddMmkw',
    'rvXefta',
    'W70jkdJcNq',
    'DcdcJa/cQq',
    'W6NcJ8kqEvy',
    'eeBdK2fp',
    'WPPqcx4y',
    'iCktWRKYAG',
    'W5NdN8kNEuu',
    'W5/dMmkmW6vnW6/cH8oItW',
    'W5hcRt1Lnq',
    'WRaqWO8hW6e',
    'rsu/W7ZdPG',
    'CmoaomoAWQq',
    'jCkfWPuCW70',
    'WR5GzSkGWR4',
    'W4pdKCk1yhi',
    'W4NcJmkzwNS',
    'WOZcOCodhW',
    'tZpcLf4',
    'WRZdPJpdHLa',
    'W7m7WPlcTK0',
    'WPRcLmoFW4LN',
    'WP1EW5ldJq',
    'WRTSW7ekua',
    'jSkBWOS5W7y',
    'WOLEW5fdlq',
    'W6pcU8kzgrS',
    'W4NcQSkbWO4',
    'W6FdOSk2qx8',
    'f2HjjHW',
    'EdRcVbBcTG',
    'WRHix8oyW5y',
    'W61ei8o0WRC',
    'W77dRCkaB2S',
    'W6JdTLv+zG',
    'WPXoFNhdJG',
    'lCoJcai',
    'EYRcLWBcQq',
    'W5NcNSkEz0u',
    'yZVcOthcTW',
    'omkYWRWQW4S',
    'nI7cKmkvpG',
    'WOhcHCkVWRpdRq',
    'WQu4h20v',
    'WPuXjMKj',
    'W47cTrbPgq',
    'uSk8irbk',
    'WPNdPYNdOxy',
    'W5GkWPpdIt7dICodW7/cSq',
    'vmoalCoxWOO',
    'WPXVW6SCCq',
    'jrhcUmkSeW',
    'WO5MtSkSWO4',
    'z1/dKcmu',
    'sSk7WPWUza',
    'xYCsW6ddIG',
    'WQHJbCowWO4',
    'zwNdKColDvFcT8k8WQTtWOZcTmkJ',
    'FYxcP25a',
    'zxelqI4',
    'W6TBbmoxWPy',
    'gtpcRmk6cq',
    'WRnorCkMW6y',
    'WQDNzSkTW7u',
    'WQCNp2GO',
    'dCkqWQWjW74',
    'WOb6qx/dHW',
    'vLr8abW',
    'W53cKSk2cq',
    'WQiuWQSLW6C',
    'WPH+yhWi',
    'WOnzW6JdIgK',
    'WRjwW7y/xq',
    'WRWdW590ja',
    'v3ncnG',
    'W7FdJvy/kW',
    'EKHsW5ZdIW',
    'W5RcTr1+aG',
    'q3zdgGa',
    'W7NcOmkbWPvL',
    'WPjohG',
    'WRX7u8k1W5i',
    'oSomWR1ezYJcSL/cIfxcRbZdIW',
    'WO9qC8kXW7y',
    'a8kXWR0nBG',
    'W4eBW4xdGmkp',
    'W7xcGSk9nJS',
    'BqtcIItcIq',
    'WOuJW6zdbq',
    'WQaibYG',
    'rCohev9x',
    'WOPDW7ZdSua',
    'gg7dINjK',
    'nJJcHSk7iq',
    'hvpdKGtdGa',
    'vgPCpHa',
    'W5KBWQ/cPW',
    'AJ3cGvnL',
    'W6VdRYvlmW',
    'nhpdN2O',
    'W5JdNN8Oaa',
    'W78BW4FdO8kv',
    'sw7dHXSq',
    'WODJW4tdKra',
    'fvdcGehcKq',
    'WO5qySkHWPK',
    'uSksW5eNbq',
    'arHFhw0',
    'vmkdEHHt',
    'WPRcUCosW5TR',
    'WQDdveBdTa',
    'WPLheSosWQK',
    'W43cSJnAha',
    'u8kFWOWmqG',
    'W5NcP8oylmk+',
    'W75QWR1sya',
    'htVcLCk1eG',
    'WORdQKemBW',
    'W5SFkbhcRW',
    'W5VcLCkHC3y',
    'WQHNW4LpeG',
    'ASkUdWfl',
    'WPvse2qx',
    'EHdcR0rT',
    'W5RdLSknWO86WOZdG8okDCkODxPk',
    'W5ndWOLjBq',
    'WO1nFehdKW',
    'WRnvW4ZdMMq',
    'W6TBbmoxWO0',
    'WQZdQwyGvW',
    'BujuW4xdSW',
    't8kTW4ammq',
    'iIH+dxS',
    'W5GDWQ/cSfm',
    'ntFcJSkD',
    'WPiyk1mw',
    'WRrpWOry',
    'yq7cObxcIa',
    'o8kVWOSBW4O',
    'mctcP8kvnW',
    'W6PRWPDAvG',
    'aCkxWRa1ta',
    'FNiRrG4',
    'z2WSqqa',
    'WOD1W5hdJcu',
    'btRcGCkZea',
    'WRtdUuebwa',
    'W5RcOSk3WOHr',
    'bmkKWP4/W5i',
    'aSoxcHLT',
    'WPDfW6L3',
    'yKfqW5VdVW',
    'W4tcS8klBg8',
    'vu/dJJS',
    'WOvBr8kTWOe',
    'ySkAW6i',
    'W6/cS8o/aSkp',
    'emo/WO5npG',
    'W7BcPCkrpru',
    'j8kJWPSJDq',
    'smkKxWbh',
    'WPfoW6RdMgK',
    'cCogitbX',
    'W6uqps7cNG',
    'WPiidIldUa',
    'W51/j8oMWQu',
    'ESoLW51MWO/cVKmUDmoQWQBdMmoN',
    'WOBcJ8kiWO7dSq',
    'WPuQj2Hv',
    'uZZcMdFcPW',
    'A20SFb4',
    'W4X9pmomWPG',
    'W5JdMHbvlq',
    'bmkWWQOAW6m',
    'nmoNjXjb',
    'W7WcW40qWQC',
    'W6yCq8ke',
    'rvLhW7tdGa',
    'fSkjCmkCW5NcI8k0WPRdLmoEW5X2',
    'WR0TW55gkq',
    'DSkCWR8Swa',
    'fvNdQXldVG',
    'WPVcKCoqW5r8',
    'WOKvWO8uW7e',
    'WR8vmNqL',
    'D0HqW5i',
    'WOddHtFdGa',
    'AL9h',
    'WQSFWPqa',
    'WOuNW4voka',
    'W7hcMCkJruW',
    'W7hcV8kXpY0',
    'W4PrhSo3WO8',
    'WR1XW4JdIvK',
    'DsBcLWu',
    'b17cJaVdLG',
    'vwCUzIi',
    'WODzrvNdSq',
    'js7cOCkFeW',
    'WO1pDmkg',
    'W7lcQSkTyxi',
    'WPO7jN0j',
    'BspcM0Du',
    'WOPyW7boca',
    'W4RcPGP9jG',
    'kCo+gsiG',
    'DmkOwIf/',
    'A8oimmooWOm',
    'nMtdTt/dOa',
    'imonisar',
    'ESkFWO0owa',
    'WOjyW5ldRfC',
    'lSoHxuGlWRfmDCkaWPxcGmoR',
    'WPyHvSo3WRu',
    'ySkAW6mA',
    'W6ZcRbvYoa',
    'dGqLW7JdUYLk',
    'W4yfW7RdJ8k0',
    'lYxdHIPP',
    'x8kfWQCixq',
    'WQ9VW4rohW',
    'bdXrfwm',
    'WPnheSkbW6W',
    'x8kfWPOvwG',
    'c8kzACkbW4m',
    'yYRcIHu',
    'D39yaqq',
    'WObcW5Dbcq',
    'W4/dGd1hfq',
    'jCkBWPaIEq',
    'WO15y1hdGq',
    'W7TebSonWPy',
    'WR5BumkmWOm',
    'WP/dTJ7dKwy',
    'W4RcOXnvd1/cSdGUWPlcIeJdLq',
    'W6LegSoiWPS',
    'W491eSoOWRi',
    'rmkghaLv',
    'atlcUmkQca',
    'WRZdGZddReC',
    'kH5Zchi',
    'pSkOWOWQW5y',
    'mbZcGSk4nG',
    'W5xdLN8Ooq',
    'WQiHW4HanG',
    'W4ZcPmkkWPG',
    'WORcJSkSWPhdTa',
    'nmo3fcTg',
    'WQqrWP8',
    'W7TiWRZdO8k4',
    'omk4WPWQ',
    'WQHvqgVdKa',
    'WOvfz8kXWO8',
    'wYtcMv9T',
    'x3b/gXG',
    'W4fyW5nCeG',
    'r8odjCoaWQ8',
    'guFdRZ3dOq',
    'WO9fsmkWW4i',
    'b8k8WRmSsa',
    'ztRcMHxcSG',
    'W4BcHCkgWPzo',
    'BgC6wbq',
    'rL1bW5JdQq',
    'WRfAW40Hva',
    'WPDoc8kzW6a',
    'dhzbuJq',
    'xwLyotO',
    'WPhdJGddRMi',
    'W6/dRSk+FW',
    'FmkUW6ybba',
    'WQ1pqhVdKa',
    'W6HwmXRdLq',
    'WPLggW',
    'omk7WOesBa',
    'qd7cJNnK',
    'rCkWWO8EwG',
    'uGVcGbVcQq',
    'W5ZcMCkBqgm',
    'W4KLW4ldJmkb',
    'W43cT8oNiSkQ',
    'WP8Wog8j',
    'WQP5lCkzW6W',
    'WQGaW7vZda',
    'W43cRSoDj8kb',
    'W47cGmkSjdq',
    'WPKnW4nKgG',
    'BhyPqXK',
    'WPpdJCkHWRVcRG',
    'BCkTrrLz',
    'WP0FmuSe',
    'W6VdVcfojq',
    'h0ZdJbVdJq',
    'bCoAna',
    'W7xdKe8Tgq',
    'W48DWPlcT04',
    'DwGiyWy',
    'W6eWkH/cRG',
    'vmkZpIfE',
    'zcRcMbpcVG',
    'WO1EW4mHxG',
    'WQpdQfiUDG',
    'f3RdKwLr',
    'WOOMW6T0nW',
    'W5HOWOj8DG',
    'W7xdRwmojG',
    'fwnvWRJcLW',
    'WOVcRSkPWRBdQa',
    'tmkCjY12',
    'WQePgM8n',
    'u8kqpW5l',
    'WPJdUw8Vtq',
    'WPu2kxmt',
    'WQn/W4ldNaK',
    'WQZdMJpdOvy',
    'h8ormI1G',
    'W74ZW7VdLCkN',
    'WO1AW5m7wG',
    'W5NcRmkwWPi',
    'oSkzWOCOW48',
    'W5aSW63dV8k5',
    'W6ehW4FdSCk/',
    'WPDqW7X9ba',
    'WObDv3eo',
    'W6BdRfWgia',
    't0NdIsyT',
    'zbJcHwjB',
    'W4DVEMOYW6zwW4dcIq',
    'WP9QW4/dRem',
    'bSofcbug',
    'pxFdJNf6',
    'WRrnW5vakG',
    'W6pdOJy',
    'ywuQtctdPCoFW67dH8ocWRK',
    'WP9zW5m8Ea',
    'ACkSdW9v',
    'WPFdTeW',
    'W7K2W5ldM8k/',
    'y8kulW5j',
    'jr5SpNm',
    'WQPXW5hdReq',
    'W6pdSfu6',
    'CSo5gCoKWQK',
    'trddNtZcHG',
    'h8kLWQStuG',
    'WQldNCo9iG',
    'xsNcNrpcMq',
    'WPNdJSoEbtrQWQrpW79TWRJdTSklWPi',
    'W4WSbJlcKG',
    'ECkjWO0/zq',
    'smkfWPyK',
    'W7ldR8kiva',
    'EmkTaWKs',
    'WQP+W5uRFG',
    'W5ZcRmocp8kx',
    'W4zZCxTq',
    'vuxdRJSW',
    'WPdcICk1WP4',
    'WQhcK8kUWR/dRG',
    'lhRdPd/dRq',
    'WOPPuCkOW5W',
    'smoNo8okWQS',
    'WQblW5ldK3O',
    'xCkFW58Wja',
    'shT3W5BdIG',
    'p8kFWPWdBa',
    'W6fAdq',
    'WQLpW4nKmG',
    'W79Lj8ovWRq',
    'W67cI8ktt0G',
    's1vrW5NdTa',
    'W5VcJH1IiG',
    'EGmWW5NdIa',
    'W6ldJ8kgsLK',
    'xhrl',
    'W59WWQnevW',
    'WP3cGSofW5rn',
    'WRFdO3WVBa',
    'W57cPSkOFMu',
    'W4GZlH7cLW',
    'W7ldPaHqhW',
    'CHitW6tdNq',
    'WObvW5hdJau',
    'WOarWO0zW7m',
    'W4VcJSoNlCk/',
    'WOTyyCkyW4K',
    'WQpdPaldVuS',
    'WP1JuSkBWR4',
    'WP/dTCotW4KtD8oTW6HkW73cVW',
    'W4xcRSkwWOHc',
    'W5pdQGvWcG',
    'u8o1aSonWQe',
    'DsZcHCknmG',
    'kLhdLqRdMG',
    'jCkNW7bpnG',
    'W6CjiY3cJG',
    'W7pcKSkHmby',
    'W6C6W77dVSk5',
    'WQvdW7BdSIy',
    'W5RcMmk6dZC',
    'WOzsW5vnfW',
    'CZNcHxTL',
    'WOD6W6ldMbO',
    'ncRcJ8klBW',
    'w2qyyae',
    'pdjkav8',
    'W6/dRSkW',
    'W7mWoH7cRa',
    'WOqioCksW4u',
    'W78MWO3cPWBdJmkrfCklW5CUW6xdRa',
    'WQmAW5TseG',
    'y8k+hrjp',
    'rSohcCoLWOO',
    'Emk2aGHv',
    'WROhEYFdIa',
    'WOC6W7Hafq',
    'WP5BcmkaW7y',
    'BhTcarS',
    'WOhcJ8ovW4K',
    'W5LWaCoaWOy',
    'xSk/W4G3fa',
    'u8kxW7WmiG',
    'W704WQpcPuy',
    'BYhcJGRcQG',
    'WPDCW5uKva',
    'WQDosq',
    'FWZcK0bK',
    'gfldL2TY',
    'WOVcPmkuWPVdHa',
    'WQnnW5VdNtO',
    'W7OYWQehW5pdGhm',
    'W7PAsSoqWOO',
    'WQ1dW7DFpG',
    'WPfWW5XDma',
    'o8oZfGm',
    'WOxcImkYWPBdPG',
    'v3OuCJS',
    'prtcK8kCpG',
    'W4xcMCkfz0O',
    'Et3cKXFcQW',
    'wrRcNe9n',
    'WQKoWOGaW68',
    'bHXKpN4',
    's8oAeCo/WPa',
    'lSkPWQ0UW5u',
    'omo5da4g',
    'AH/cV19K',
    'iJm9dxW',
    'tZNcJgbn',
    'B3a0xHG',
    'W43cUa5bjW',
    'W5/cQSomp8kD',
    'WPTEW5xdJam',
    'W5JcH8kKedO',
    'W5BcLCoEdSkS',
    'W4aEbW/cJG',
    'W7VdRszvcq',
    'WRnSp2eT',
    'zMZdL8okCvBcTmodWQPVWQ/cHmkZWOi',
    'j8k3WQasFW',
    'rx7dNWS',
    'WR/dTuavFW',
    'WObfW5HBeW',
    'W77dQsjw',
    'lCkKWQikzW',
    'WRXjW7ClAa',
    'WRRcH8kOWPFdJa',
    'jmotidjm',
    'W7buWRL3rq',
    'h8oVpbiy',
    'FSkvDHL7',
    'WPL3W6ddIaS',
    'tfj0W5JdIa',
    'W6aiA3ldKa',
    'kSoFcYH2',
    'eL3dS2XZ',
    'zdVcMbxcUa',
    'WOT7BSkQWPa',
    'WR/cMSkRWO/dKa',
    'WQTzACkuWO4',
    'W6boWOTavG',
    'W6ZdK00Afa',
    'AsiWW6pdPW',
    'r29pjGy',
    'v1uQsWe'
];
const decoder = new Rc4StringDecoder(stringArray, -289);
const _0x1322 = decoder.getString.bind(decoder);
(function (_0x4a3082, _0x3886fa) {
    var _0x53dc8f = _0x4a3082();
    while (!![]) {
        try {
            var _0x2447b0 = parseInt(_0x1322(0x14d, '6%(!')) / (-0x696 + -0x128d + -0xc92 * -0x2) +
                (parseInt(_0x1322(0x2a9, '*Soy')) / (0x1 * -0x9bb + 0x138b * 0x1 + -0x5 * 0x1f6)) *
                    (-parseInt(_0x1322(0x1ad, 'BDF2')) / (-0x1399 + 0x10e2 * 0x1 + 0x2ba)) +
                -parseInt(_0x1322(0x134, 'dv!W')) / (0x10e9 * 0x1 + 0x2 * -0xf95 + 0xe45) +
                (-parseInt(_0x1322(0x2e5, 'z5Ho')) / (0x1 * 0x1c0 + -0x22f8 + 0x213d)) *
                    (parseInt(_0x1322(0x27f, 'm]gH')) / (0xf8 * 0x5 + -0x3d * 0x95 + 0x623 * 0x5)) +
                parseInt(_0x1322(0x294, 'oa%j')) / (0x9 * 0x3cb + -0x1 * -0x1097 + 0x1 * -0x32b3) +
                (-parseInt(_0x1322(0x242, '1a5^')) / (0x25a0 + -0x84b + -0x1d4d)) *
                    (parseInt(_0x1322(0x2f4, '^PB8')) / (-0xa8f + 0x1 * 0x432 + 0x666)) +
                parseInt(_0x1322(0x2c5, 'dv!W')) / (-0x13f1 + 0x1 * -0x17ce + -0x2bc9 * -0x1);
            if (_0x2447b0 === _0x3886fa)
                break;
            else
                _0x53dc8f['push'](_0x53dc8f['shift']());
        }
        catch (_0x2f101b) {
            _0x53dc8f['push'](_0x53dc8f['shift']());
        }
    }
})(() => stringArray, 0x379 * 0x6a3 + -0x3a * -0x6037 + -0x77b01 * 0x4);
console.log(decoder.getString(0x24b, '^PB8'));
