"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StringRevealer = void 0;
const t = __importStar(require("@babel/types"));
const traverse_1 = __importDefault(require("@babel/traverse"));
const transformation_1 = require("../transformation");
const declaration_1 = require("../../helpers/declaration");
const generator_1 = __importDefault(require("@babel/generator"));
const expression_1 = require("../../helpers/expression");
const vm2_1 = require("vm2");
class StringRevealer extends transformation_1.Transformation {
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log) {
        const self = this;
        (0, traverse_1.default)(this.ast, {
            enter(path) {
                if (self.isStringArrayFunction(path.node)) {
                    const name = path.node.id.name;
                    const binding = path.scope.getBinding(name);
                    if (!binding) {
                        return;
                    }
                    let wrapperFunctions = [];
                    let rotateCall;
                    for (const referencePath of binding.referencePaths) {
                        // ignore call to function from within
                        if (referencePath.scope == path.scope) {
                            continue;
                        }
                        if (referencePath.parentKey == 'callee') {
                            const functionParent = referencePath.getFunctionParent();
                            if (!functionParent) {
                                return;
                            }
                            if (self.isSimpleStringArrayWrapperFunction(functionParent.node, name) ||
                                self.isComplexStringArrayWrapperFunction(functionParent.node, name)) {
                                wrapperFunctions.push(functionParent);
                            }
                            else {
                                return; // unknown reference to string array function found
                            }
                        }
                        else if (referencePath.parentKey == 'arguments') {
                            const parentPath = referencePath.parentPath;
                            if (self.isRotateStringArrayCall(parentPath.node, name)) {
                                rotateCall =
                                    parentPath.parentPath;
                            }
                            else {
                                return; // unknown reference to string array function found
                            }
                        }
                        else {
                            return; // unknown reference to string array function found
                        }
                    }
                    // ensure there is at least one wrapper function
                    if (wrapperFunctions.length == 0) {
                        log(`Failed to find a string concealer wrapper function`);
                        return;
                    }
                    const wrapperFunctionNames = wrapperFunctions.map(w => w.node.id.name);
                    const wrapperBindings = wrapperFunctions.map((w, i) => w.scope.getBinding(wrapperFunctionNames[i]));
                    if (wrapperBindings.find(w => !w)) {
                        log(`Failed to find string concealer wrapper functions`);
                        return;
                    }
                    const statements = rotateCall
                        ? [path.node, ...wrapperFunctions.map(w => w.node), rotateCall.node]
                        : [path.node, ...wrapperFunctions.map(w => w.node)];
                    const code = (0, generator_1.default)(t.program(statements), { minified: true }).code;
                    const vm = new vm2_1.VM({
                        timeout: 1e3,
                        allowAsync: false
                    });
                    try {
                        vm.run(code);
                    }
                    catch (err) {
                        log(`Error evaluating string concealer functions: ${err}`);
                        return; // error evaluating string concealer code
                    }
                    let failedReplacement = false;
                    for (let i = 0; i < wrapperFunctions.length; i++) {
                        const wrapperFunction = wrapperFunctions[i];
                        const wrapperBinding = wrapperBindings[i];
                        const wrapperName = wrapperFunctionNames[i];
                        for (const referencePath of wrapperBinding.referencePaths) {
                            const functionParent = referencePath.getFunctionParent();
                            const outerFunctionParent = functionParent && functionParent.getFunctionParent();
                            const parentPath = referencePath.parentPath;
                            if ((functionParent &&
                                (functionParent.node == wrapperFunction.node ||
                                    (rotateCall &&
                                        functionParent.node ==
                                            rotateCall.node.expression
                                                .callee))) ||
                                (outerFunctionParent &&
                                    outerFunctionParent.node == wrapperFunction.node)) {
                                continue;
                            }
                            else if (!parentPath ||
                                !self.isStringArrayWrapperCall(parentPath.node)) {
                                failedReplacement = true;
                            }
                            else {
                                try {
                                    const args = t.arrayExpression(parentPath.node.arguments);
                                    const code = `${wrapperName}(...${(0, generator_1.default)(args).code})`;
                                    const value = vm.run(code);
                                    if (typeof value == 'string') {
                                        parentPath.replaceWith(t.stringLiteral(value));
                                        self.setChanged();
                                    }
                                    else {
                                        failedReplacement = true;
                                    }
                                }
                                catch (_a) {
                                    failedReplacement = true;
                                }
                            }
                        }
                    }
                    if (!failedReplacement) {
                        path.remove();
                        wrapperFunctions.map(w => w.remove());
                        if (rotateCall) {
                            rotateCall.remove();
                        }
                        self.setChanged();
                    }
                }
                else if (self.isEscapedStringLiteral(path.node)) {
                    delete path.node.extra;
                    self.setChanged();
                }
            }
        });
        return this.hasChanged();
    }
    /**
     * Returns whether a node is the function that splits and returns the
     * string array.
     * @param node The AST node.
     * @returns Whether.
     */
    isStringArrayFunction(node) {
        return (t.isFunctionDeclaration(node) &&
            t.isBlockStatement(node.body) &&
            node.body.body.length == 3 &&
            (0, declaration_1.isDeclarationOrAssignmentStatement)(node.body.body[0], t.isIdentifier, (node) => t.isArrayExpression(node) || // explicit string array
                (t.isCallExpression(node) && // creating string array by splitting a string
                    t.isMemberExpression(node.callee) &&
                    t.isStringLiteral(node.callee.object) &&
                    t.isIdentifier(node.callee.property) &&
                    node.callee.property.name == 'split' &&
                    node.arguments.length == 1 &&
                    t.isStringLiteral(node.arguments[0]))) &&
            (0, declaration_1.isDeclarationOrAssignmentStatement)(node.body.body[1], t.isIdentifier, (node) => t.isFunctionExpression(node) &&
                t.isBlockStatement(node.body) &&
                node.body.body.length == 1 &&
                t.isReturnStatement(node.body.body[0]) &&
                t.isIdentifier(node.body.body[0].argument)) &&
            t.isReturnStatement(node.body.body[2]) &&
            t.isCallExpression(node.body.body[2].argument) &&
            t.isIdentifier(node.body.body[2].argument.callee) &&
            node.body.body[2].argument.arguments.length == 0);
    }
    /**
     * Returns whether a node is the function that acts as a wrapper around
     * the string array to return strings by index.
     * @param node The AST node.
     * @param stringArrayFunctionName The name of the string array function.
     * @returns Whether.
     */
    isSimpleStringArrayWrapperFunction(node, stringArrayFunctionName) {
        return (t.isFunctionDeclaration(node) &&
            t.isBlockStatement(node.body) &&
            node.body.body.length == 3 &&
            (0, declaration_1.isDeclarationOrAssignmentStatement)(node.body.body[0], t.isIdentifier, (node) => t.isCallExpression(node) &&
                t.isIdentifier(node.callee) &&
                node.callee.name == stringArrayFunctionName &&
                node.arguments.length == 0) &&
            (0, declaration_1.isDeclarationOrAssignmentStatement)(node.body.body[1], t.isIdentifier, (node) => t.isFunctionExpression(node) &&
                t.isBlockStatement(node.body) &&
                node.body.body.length == 3 &&
                (0, declaration_1.isDeclarationOrAssignmentStatement)(node.body.body[0], t.isIdentifier, (node) => t.isBinaryExpression(node) &&
                    node.operator == '-' &&
                    t.isIdentifier(node.left) &&
                    t.isNumericLiteral(node.right)) &&
                (0, declaration_1.isDeclarationOrAssignmentStatement)(node.body.body[1], t.isIdentifier, (node) => t.isMemberExpression(node) &&
                    t.isIdentifier(node.object) &&
                    t.isIdentifier(node.property)) &&
                t.isReturnStatement(node.body.body[2]) &&
                t.isIdentifier(node.body.body[2].argument)) &&
            t.isReturnStatement(node.body.body[2]) &&
            t.isCallExpression(node.body.body[2].argument) &&
            t.isIdentifier(node.body.body[2].argument.callee) &&
            node.body.body[2].argument.arguments.length == 2 &&
            t.isIdentifier(node.body.body[2].argument.arguments[0]) &&
            t.isIdentifier(node.body.body[2].argument.arguments[1]));
    }
    /**
     * Returns whether a node is the function that acts as a wrapper around
     * the string array to return strings by index.
     * @param node The AST node.
     * @param stringArrayFunctionName The name of the string array function.
     * @returns Whether.
     */
    isComplexStringArrayWrapperFunction(node, stringArrayFunctionName) {
        return (t.isFunctionDeclaration(node) &&
            t.isBlockStatement(node.body) &&
            node.body.body.length == 3 &&
            (0, declaration_1.isDeclarationOrAssignmentStatement)(node.body.body[0], t.isIdentifier, (node) => t.isCallExpression(node) &&
                t.isIdentifier(node.callee) &&
                node.callee.name == stringArrayFunctionName &&
                node.arguments.length == 0) &&
            (0, declaration_1.isDeclarationOrAssignmentStatement)(node.body.body[1], t.isIdentifier, (node) => t.isFunctionExpression(node) &&
                t.isBlockStatement(node.body) &&
                node.body.body.length >= 4 &&
                (0, declaration_1.isDeclarationOrAssignmentStatement)(node.body.body[0], t.isIdentifier, (node) => t.isBinaryExpression(node) &&
                    node.operator == '-' &&
                    t.isIdentifier(node.left) &&
                    t.isNumericLiteral(node.right)) &&
                (0, declaration_1.isDeclarationOrAssignmentStatement)(node.body.body[1], t.isIdentifier, (node) => t.isMemberExpression(node) &&
                    t.isIdentifier(node.object) &&
                    t.isIdentifier(node.property)) &&
                t.isIfStatement(node.body.body[2]) &&
                t.isIfStatement(node.body.body[node.body.body.length - 2]) &&
                t.isReturnStatement(node.body.body[node.body.body.length - 1])) &&
            t.isReturnStatement(node.body.body[2]) &&
            t.isCallExpression(node.body.body[2].argument) &&
            t.isIdentifier(node.body.body[2].argument.callee) &&
            node.body.body[2].argument.arguments.length == 2 &&
            t.isIdentifier(node.body.body[2].argument.arguments[0]) &&
            node.body.body[2].argument.arguments[0].name == 'arguments' &&
            t.isIdentifier(node.body.body[2].argument.arguments[1]));
    }
    /**
     * Returns whether a node is a call to rotate the string array.
     * @param node The AST node.
     * @param stringArrayFunctionName The name of the string array function.
     * @returns Whether.
     */
    isRotateStringArrayCall(node, stringArrayFunctionName) {
        return (t.isCallExpression(node) &&
            node.arguments.length == 2 &&
            t.isIdentifier(node.arguments[0]) &&
            node.arguments[0].name == stringArrayFunctionName &&
            t.isNumericLiteral(node.arguments[1]) &&
            t.isFunctionExpression(node.callee) &&
            ((0, generator_1.default)(node.callee).code.match(/parseInt/g) || []).length > 5);
    }
    /**
     * Returns whether a node is a call of the string array wrapper function.
     * @param node The AST node.
     * @returns Whether.
     */
    isStringArrayWrapperCall(node) {
        return (t.isCallExpression(node) &&
            t.isIdentifier(node.callee) &&
            (node.arguments.length == 1 || node.arguments.length == 2) &&
            node.arguments.every(node => t.isNumericLiteral(node) ||
                t.isStringLiteral(node) ||
                (0, expression_1.isNegativeNumericLiteral)(node)));
    }
    /**
     * Returns whether a node is an escaped string literal.
     * @param node The AST node.
     * @returns Whether.
     */
    isEscapedStringLiteral(node) {
        return (t.isStringLiteral(node) &&
            node.extra != undefined &&
            typeof node.extra.rawValue == 'string' &&
            typeof node.extra.raw == 'string' &&
            node.extra.raw.replace(/["']/g, '') != node.extra.rawValue);
    }
}
exports.StringRevealer = StringRevealer;
StringRevealer.properties = {
    key: 'stringRevealing',
    rebuildScopeTree: true
};
