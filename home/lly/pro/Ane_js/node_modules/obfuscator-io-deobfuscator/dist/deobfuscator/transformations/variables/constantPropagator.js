"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstantPropgator = void 0;
const t = __importStar(require("@babel/types"));
const traverse_1 = __importDefault(require("@babel/traverse"));
const transformation_1 = require("../transformation");
const variable_1 = require("../../helpers/variable");
const misc_1 = require("../../helpers/misc");
class ConstantPropgator extends transformation_1.Transformation {
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log) {
        const self = this;
        (0, traverse_1.default)(this.ast, {
            enter(path) {
                // note that in general this is unsafe, should perform data flow analysis to handle params that are constants regardless of their runtime value
                const variable = (0, variable_1.findConstantVariable)(path, isLiteral);
                if (!variable) {
                    return;
                }
                // avoid propagating params that are assigned to within branches
                if (variable instanceof variable_1.ConstantAssignmentVariable) {
                    if (variable.binding.path.parentKey == 'params') {
                        const functionParent = variable.binding.path.getStatementParent();
                        const parentPath = path.getStatementParent();
                        if (parentPath.parent != functionParent.node.body) {
                            return;
                        }
                    }
                }
                for (const referencePath of variable.binding.referencePaths) {
                    const expression = (0, misc_1.copyExpression)(variable.expression);
                    referencePath.replaceWith(expression);
                    self.setChanged();
                }
                variable.remove();
            }
        });
        return this.hasChanged();
    }
}
exports.ConstantPropgator = ConstantPropgator;
ConstantPropgator.properties = {
    key: 'constantPropagation',
    rebuildScopeTree: true
};
/**
 * Returns whether a node is a literal that can be safely propagated.
 * @param node The node.
 * @returns Whether.
 */
const isLiteral = (node) => {
    return t.isLiteral(node) && !t.isRegExpLiteral(node);
};
