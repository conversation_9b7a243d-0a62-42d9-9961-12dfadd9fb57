{"version": 3, "file": "antiTamperRemover.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/antiTamper/antiTamperRemover.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+DAAqD;AACrD,sDAA0F;AAC1F,qDAAuC;AAEvC,MAAa,iBAAkB,SAAQ,+BAAc;IAMjD;;;OAGG;IACI,OAAO,CAAC,GAAgB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAA,kBAAQ,EAAC,IAAI,CAAC,GAAG,EAAE;YACf,KAAK,CAAC,IAAI;;gBACN;;;;;;;;;;;;;;;;;;;kBAmBE;gBACF,MAAM,WAAW,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;gBAC9C,MAAM,eAAe,GAAG,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;oBACjD,CAAC,CAAC,kBAAkB,CAChB,WAAW,EACX,CAAC,CAAC,cAAc,CACZ,CAAC,CAAC,kBAAkB,CAChB,IAAI,EACJ,EAAE,EACF,CAAC,CAAC,cAAc,CAAC;wBACb,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;4BACzB,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;yBAC/D,CAAC;wBACF,CAAC,CAAC,eAAe,CACb,CAAC,CAAC,kBAAkB,CAChB,IAAI,EACJ,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,EAChC,CAAC,CAAC,cAAc,CAAC;4BACb,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;gCACzB,CAAC,CAAC,kBAAkB,CAChB,CAAC,CAAC,UAAU,EAAE,EACd,CAAC,CAAC,qBAAqB,CACnB,CAAC,CAAC,UAAU,EAAE,EACd,CAAC,CAAC,kBAAkB,CAChB,IAAI,EACJ,EAAE,EACF,CAAC,CAAC,cAAc,EAAE,CACrB,EACD,CAAC,CAAC,kBAAkB,CAChB,IAAI,EACJ,EAAE,EACF,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,CACvB,CACJ,CACJ;6BACJ,CAAC;4BACF,CAAC,CAAC,mBAAmB,CACjB,CAAC,CAAC,oBAAoB,CAClB,GAAG,EACH,CAAC,CAAC,UAAU,EAAE,EACd,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAC1B,CACJ;4BACD,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;yBACpC,CAAC,CACL,CACJ;qBACJ,CAAC,CACL,EACD,EAAE,CACL,CACJ;iBACJ,CAAC,CAAC;gBAEH;;;;;kBAKE;gBACF,MAAM,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;gBACpD,MAAM,iBAAiB,GAAG,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;oBACnD,CAAC,CAAC,kBAAkB,CAChB,iBAAiB,EACjB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE;wBAC7B,CAAC,CAAC,cAAc,EAAE;wBAClB,CAAC,CAAC,kBAAkB,CAChB,IAAI,EACJ,EAAE,EACF,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAC5D;qBACJ,CAAC,CACL;iBACJ,CAAC,CAAC;gBAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kBAyCE;gBACF,MAAM,mBAAmB,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;gBACtD,MAAM,mBAAmB,GAAG,CAAC,CAAC,mBAAmB,CAC7C,CAAC,CAAC,cAAc,CACZ,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE;oBAC7B,CAAC,CAAC,cAAc,EAAE;oBAClB,CAAC,CAAC,kBAAkB,CAChB,IAAI,EACJ,EAAE,EACF,CAAC,CAAC,cAAc,CAAC;wBACb,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;4BACzB,CAAC,CAAC,kBAAkB,CAChB,CAAC,CAAC,UAAU,EAAE,EACd,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAC1C;yBACJ,CAAC;wBACF,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;4BACzB,CAAC,CAAC,kBAAkB,CAChB,CAAC,CAAC,UAAU,EAAE,EACd,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAC1C;yBACJ,CAAC;wBACF,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;4BACzB,CAAC,CAAC,kBAAkB,CAChB,CAAC,CAAC,UAAU,EAAE,EACd,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,CACxC;yBACJ,CAAC;wBACF,CAAC,CAAC,WAAW,CACT,CAAC,CAAC,iBAAiB,EAAE,EACrB,CAAC,CAAC,cAAc,CAAC;4BACb,CAAC,CAAC,mBAAmB,CACjB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE;gCAC7B,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC;6BACvB,CAAC,CACL;yBACJ,CAAC,EACF,CAAC,CAAC,cAAc,CAAC;4BACb,CAAC,CAAC,mBAAmB,CACjB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,CACvC;yBACJ,CAAC,CACL;qBACJ,CAAC,CACL;iBACJ,CAAC,EACF,EAAE,CACL,CACJ,CAAC;gBAEF;;;;;;;;;;;;;;;;;;;;;kBAqBE;gBACF,MAAM,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;gBACpD,MAAM,iBAAiB,GAAG,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;oBACnD,CAAC,CAAC,kBAAkB,CAChB,iBAAiB,EACjB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE;wBAC7B,CAAC,CAAC,cAAc,EAAE;wBAClB,CAAC,CAAC,kBAAkB,CAChB,IAAI,EACJ,EAAE,EACF,CAAC,CAAC,cAAc,CAAC;4BACb,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;gCACzB,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC;6BAC7C,CAAC;4BACF,CAAC,CAAC,YAAY,EAAE;4BAChB,CAAC,CAAC,mBAAmB,EAAE;4BACvB,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;gCACzB,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC;6BAC5D,CAAC;4BACF,CAAC,CAAC,YAAY,EAAE;yBACnB,CAAC,CACL;qBACJ,CAAC,CACL;iBACJ,CAAC,CAAC;gBAEH,IAAI,iBAAiB,GAAG,KAAK,CAAC;gBAC9B,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,OAAQ,CAAC,IAAI,CAAC,CAAC;oBACjE,IAAI,OAAO,EAAE,CAAC;wBACV,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;4BAC7C,MAAM,MAAM,GAAG,SAAS,CAAC,kBAAkB,EAAc,CAAC;4BAE1D,IAAI,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gCACvC,MAAM,oBAAoB,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAChD,iBAAiB,CAAC,OAAQ,CAAC,IAAI,CAClC,CAAC;gCACF,IAAI,oBAAoB,EAAE,CAAC;oCACvB,KAAK,MAAM,sBAAsB,IAAI,oBAAoB,CAAC,cAAc,EAAE,CAAC;wCACvE,MAAA,sBAAsB,CAAC,kBAAkB,EAAE,0CAAE,MAAM,EAAE,CAAC;wCACtD,IAAI,CAAC,UAAU,EAAE,CAAC;oCACtB,CAAC;gCACL,CAAC;gCACD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oCAClB,MAAM,CAAC,MAAM,EAAE,CAAC;oCAChB,IAAI,CAAC,UAAU,EAAE,CAAC;gCACtB,CAAC;4BACL,CAAC;iCAAM,IAAI,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gCAChD,oCAAoC;gCACpC,MAAM,gBAAgB,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAC5C,mBAAmB,CAAC,OAAQ,CAAC,IAAI,CACpC,CAAC;gCACF,IAAI,gBAAgB,EAAE,CAAC;oCACnB,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;oCAC/B,IAAI,CAAC,UAAU,EAAE,CAAC;gCACtB,CAAC;gCAED,0BAA0B;gCAC1B,MAAA,MAAA,MAAM;qCACD,UAAW,CAAC,kBAAkB,EAAE,0CAC/B,kBAAkB,EAAE,0CACpB,MAAM,EAAE,CAAC;4BACnB,CAAC;iCAAM,IAAI,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gCAC9C,MAAM,oBAAoB,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAChD,iBAAiB,CAAC,OAAQ,CAAC,IAAI,CAClC,CAAC;gCACF,IAAI,oBAAoB,EAAE,CAAC;oCACvB,KAAK,MAAM,sBAAsB,IAAI,oBAAoB,CAAC,cAAc,EAAE,CAAC;wCACvE,sBAAsB,CAAC,kBAAkB,EAAG,CAAC,MAAM,EAAE,CAAC;wCACtD,IAAI,CAAC,UAAU,EAAE,CAAC;oCACtB,CAAC;gCACL,CAAC;4BACL,CAAC;iCAAM,CAAC;gCACJ,gEAAgE;gCAChE,MAAM,cAAc,GAAG,MAAA,MAAM;qCACxB,iBAAiB,EAAE,0CAClB,kBAAkB,EAAE,CAAC;gCAC3B,IACI,cAAc;oCACd,iBAAiB,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAC9C,CAAC;oCACC,SAAS;gCACb,CAAC;gCAED,GAAG,CAAC,8DAA8D,CAAC,CAAC;gCACpE,iBAAiB,GAAG,IAAI,CAAC;4BAC7B,CAAC;wBACL,CAAC;oBACL,CAAC;oBAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACrB,IAAI,CAAC,MAAM,EAAE,CAAC;wBACd,IAAI,CAAC,UAAU,EAAE,CAAC;oBACtB,CAAC;gBACL,CAAC;YACL,CAAC;SACJ,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;;AAhUL,8CAiUC;AAhU0B,4BAAU,GAA6B;IAC1D,GAAG,EAAE,mBAAmB;IACxB,gBAAgB,EAAE,IAAI;CACzB,CAAC"}