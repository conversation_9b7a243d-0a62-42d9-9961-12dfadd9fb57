"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Base64StringDecoder = void 0;
const util_1 = require("../util/util");
const stringDecoder_1 = require("./stringDecoder");
class Base64StringDecoder extends stringDecoder_1.StringDecoder {
    /**
     * Creates a new base 64 string decoder.
     * @param stringArray The string array.
     * @param indexOffset The offset used when accessing elements by index.
     */
    constructor(stringArray, indexOffset) {
        super(stringArray, indexOffset);
        this.stringCache = new Map();
    }
    /**
     * Returns the type of the decoder.
     */
    get type() {
        return stringDecoder_1.DecoderType.BASE_64;
    }
    /**
     * Decodes a string.
     * @param index The index.
     * @returns The string.
     */
    getString(index) {
        const cacheKey = index + this.stringArray[0];
        if (this.stringCache.has(cacheKey)) {
            return this.stringCache.get(cacheKey);
        }
        const encoded = this.stringArray[index + this.indexOffset];
        const str = (0, util_1.base64Transform)(encoded);
        this.stringCache.set(cacheKey, str);
        return str;
    }
    /**
     * Decodes a string for the rotate string call.
     * @param index The index.
     * @returns THe string.
     */
    getStringForRotation(index) {
        if (this.isFirstCall) {
            this.isFirstCall = false;
            throw new Error();
        }
        return this.getString(index);
    }
}
exports.Base64StringDecoder = Base64StringDecoder;
//# sourceMappingURL=base64StringDecoder.js.map