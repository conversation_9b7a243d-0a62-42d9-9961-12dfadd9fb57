"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Transformation = void 0;
class Transformation {
    /**
     * Creates a new transformation.
     * @param ast The AST.
     * @param config The transformation config.
     */
    constructor(ast, config) {
        this.changed = false;
        this.ast = ast;
    }
    /**
     * Returns whether the script has been modified.
     * @returns Whether the script has been modified.
     */
    hasChanged() {
        return this.changed;
    }
    /**
     * Marks that the script has been modified.
     */
    setChanged() {
        this.changed = true;
    }
}
exports.Transformation = Transformation;
