import { LogFunction, Transformation, TransformationProperties } from '../transformation';
export declare class ObjectPacker extends Transformation {
    static readonly properties: TransformationProperties;
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log: LogFunction): boolean;
    /**
     * Searches a value for a reference to the object itself. Inlining this value
     * as an object property would be unsafe: https://github.com/ben-sb/obfuscator-io-deobfuscator/issues/39
     * @param value The value of the object property.
     * @param statementPath The path of the statement assigning the property.
     * @param arrayIndex The index of the assigning statement within the parent statement array.
     * @param referencePathSet A set of paths referencing the object being packed.
     * @returns Whether the value contains a reference to the object.
     */
    private hasSelfReference;
    /**
     * Returns whether a node is setting a property on a given object.
     * @param node The AST node.
     * @param objectName The name of the object.
     * @returns Whether.
     */
    private isPropertyAssignment;
}
