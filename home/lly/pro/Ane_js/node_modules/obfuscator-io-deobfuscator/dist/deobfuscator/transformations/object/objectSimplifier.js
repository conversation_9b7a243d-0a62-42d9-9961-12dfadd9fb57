"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectSimplifier = void 0;
const transformation_1 = require("../transformation");
const traverse_1 = __importDefault(require("@babel/traverse"));
const variable_1 = require("../../helpers/variable");
const proxyObject_1 = require("./proxyObject");
class ObjectSimplifier extends transformation_1.Transformation {
    constructor(ast, config) {
        super(ast, config);
        this.config = config;
    }
    /**
     * Executes the transformation.
     * @returns Whether any changes were made.
     */
    execute(log) {
        const self = this;
        (0, traverse_1.default)(this.ast, {
            enter(path) {
                const variable = (0, variable_1.findConstantVariable)(path, proxyObject_1.isProxyObjectExpression);
                if (!variable) {
                    return;
                }
                // check if object values are modified
                for (const referencePath of variable.binding.referencePaths) {
                    if (referencePath.parentPath &&
                        referencePath.parentPath.isMemberExpression() &&
                        referencePath.parentPath.parentPath &&
                        referencePath.parentPath.parentPath.isAssignmentExpression()) {
                        if (!self.config.unsafeReplace) {
                            log(`Not replacing object ${variable.name} as it is modified`);
                            path.skip();
                            return;
                        }
                        else {
                            log(`Unsafe replacing on object ${variable.name}`);
                        }
                    }
                }
                const proxyObject = new proxyObject_1.ProxyObject(variable);
                proxyObject.process();
                if (proxyObject.replaceUsages()) {
                    self.setChanged();
                }
            }
        });
        return this.hasChanged();
    }
}
exports.ObjectSimplifier = ObjectSimplifier;
ObjectSimplifier.properties = {
    key: 'objectSimplification'
};
