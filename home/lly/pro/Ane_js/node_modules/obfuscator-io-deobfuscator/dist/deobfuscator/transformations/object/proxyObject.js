"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProxyObject = exports.isProxyObjectExpression = void 0;
const t = __importStar(require("@babel/types"));
const misc_1 = require("../../helpers/misc");
/**
 * Returns whether a node is a proxy object.
 * @param node The node.
 * @returns Whether.
 */
const isProxyObjectExpression = (node) => {
    return t.isObjectExpression(node) && node.properties.length > 0;
};
exports.isProxyObjectExpression = isProxyObjectExpression;
class ProxyObject {
    /**
     * Creates a new proxy object.
     * @param variable The variable.
     */
    constructor(variable) {
        this.literalProperties = new Map();
        this.variable = variable;
    }
    /**
     * Finds all the object's entries which can be replaced.
     */
    process() {
        for (const property of this.variable.expression.properties) {
            if (t.isObjectProperty(property) && this.isLiteralPropertyKey(property) && t.isLiteral(property.value)) {
                const key = t.isIdentifier(property.key) ? property.key.name : property.key.value;
                this.literalProperties.set(key, property.value);
            }
        }
    }
    /**
     * Replaces references to the object.
     * @returns Whether any modifications have been made.
     */
    replaceUsages() {
        let hasModified = false;
        for (const path of this.variable.binding.referencePaths) {
            const parentPath = path.parentPath;
            if (parentPath && parentPath.isMemberExpression() && this.isLiteralMemberKey(parentPath.node)) {
                const key = t.isIdentifier(parentPath.node.property)
                    ? parentPath.node.property.name
                    : parentPath.node.property.value;
                if (this.literalProperties.has(key)) {
                    const value = this.literalProperties.get(key);
                    parentPath.replaceWith((0, misc_1.copyExpression)(value));
                    hasModified = true;
                }
            }
        }
        return hasModified;
    }
    /**
     * Returns whether an object property has a literal key.
     * @param property The object property.
     * @returns Whether.
     */
    isLiteralPropertyKey(property) {
        return (t.isStringLiteral(property.key) ||
            t.isNumericLiteral(property.key) ||
            (!property.computed && t.isIdentifier(property.key)));
    }
    /**
     * Returns whether a member expression has a literal key.
     * @param member The member expression.
     * @returns Whether.
     */
    isLiteralMemberKey(member) {
        return (t.isStringLiteral(member.property) ||
            t.isNumericLiteral(member.property) ||
            (!member.computed && t.isIdentifier(member.property)));
    }
}
exports.ProxyObject = ProxyObject;
