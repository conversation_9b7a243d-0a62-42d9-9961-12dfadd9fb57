"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropertySimplifier = void 0;
const t = __importStar(require("@babel/types"));
const traverse_1 = __importDefault(require("@babel/traverse"));
const transformation_1 = require("../transformation");
class PropertySimplifier extends transformation_1.Transformation {
    /**
     * Executes the transformation.
     * @param log The log function.
     */
    execute(log) {
        const self = this;
        (0, traverse_1.default)(this.ast, {
            MemberExpression(path) {
                if (path.node.computed &&
                    t.isStringLiteral(path.node.property) &&
                    t.isValidIdentifier(path.node.property.value)) {
                    path.node.property = t.identifier(path.node.property.value);
                    path.node.computed = false;
                    self.setChanged();
                }
            },
            ['ObjectProperty|ObjectMethod'](path) {
                if (path.node.computed &&
                    t.isStringLiteral(path.node.key) &&
                    t.isValidIdentifier(path.node.key.value)) {
                    path.node.key = t.identifier(path.node.key.value);
                    path.node.computed = false;
                    self.setChanged();
                }
                else if (path.node.computed &&
                    (t.isStringLiteral(path.node.key) || t.isNumericLiteral(path.node.key))) {
                    path.node.computed = false;
                }
            }
        });
        return this.hasChanged();
    }
}
exports.PropertySimplifier = PropertySimplifier;
PropertySimplifier.properties = {
    key: 'propertySimplification'
};
