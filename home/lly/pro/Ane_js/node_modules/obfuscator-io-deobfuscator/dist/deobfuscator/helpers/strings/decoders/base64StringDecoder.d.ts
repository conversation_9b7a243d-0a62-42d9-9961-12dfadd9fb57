import { DecoderType, StringDecoder } from './stringDecoder';
export declare class Base64StringDecoder extends StringDecoder {
    private readonly stringCache;
    /**
     * Creates a new base 64 string decoder.
     * @param stringArray The string array.
     * @param indexOffset The offset used when accessing elements by index.
     */
    constructor(stringArray: string[], indexOffset: number);
    /**
     * Returns the type of the decoder.
     */
    get type(): DecoderType;
    /**
     * Decodes a string.
     * @param index The index.
     * @returns The string.
     */
    getString(index: number): string;
    /**
     * Decodes a string for the rotate string call.
     * @param index The index.
     * @returns THe string.
     */
    getStringForRotation(index: number): string;
}
