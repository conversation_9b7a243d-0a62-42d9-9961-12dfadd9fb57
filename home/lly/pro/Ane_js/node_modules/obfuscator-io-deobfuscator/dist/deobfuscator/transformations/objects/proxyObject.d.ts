import * as t from '@babel/types';
import { ConstantVariable } from '../../helpers/variable';
import { NodePath } from '@babel/traverse';
export type ProxyObjectExpression = t.ObjectExpression;
/**
 * Returns whether a node is a proxy object.
 * @param node The node.
 * @returns Whether.
 */
export declare const isProxyObjectExpression: (node: t.Node) => node is ProxyObjectExpression;
export declare class ProxyObject {
    private readonly variable;
    private readonly literalProperties;
    private readonly proxyFunctionProperties;
    /**
     * Creates a new proxy object.
     * @param variable The variable.
     */
    constructor(variable: ConstantVariable<ProxyObjectExpression>);
    /**
     * Finds all the object's entries which can be replaced.
     */
    process(): void;
    /**
     * Returns the usages of the object.
     * @returns The usages.
     */
    getUsages(): NodePath[];
    /**
     * Attempts to replace a usage of the object.
     * @param path The path of the usage.
     * @returns Whether it was replaced.
     */
    replaceUsage(path: NodePath): boolean;
    /**
     * Returns whether an object property has a literal key.
     * @param property The object property.
     * @returns Whether.
     */
    private isLiteralPropertyKey;
    /**
     * Returns whether an object method has a literal key.
     * @param property The object method.
     * @returns Whether.
     */
    private isLiteralMethodKey;
    /**
     * Returns whether a member expression has a literal key.
     * @param member The member expression.
     * @returns Whether.
     */
    private isLiteralMemberKey;
}
