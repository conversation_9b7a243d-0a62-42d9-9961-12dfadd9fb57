{"version": 3, "file": "expressionSimplifier.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/expressions/expressionSimplifier.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,+DAAuC;AACvC,sDAA0F;AAC1F,yDAAoE;AAEpE,MAAa,oBAAqB,SAAQ,+BAAc;IAmCpD;;;OAGG;IACI,OAAO,CAAC,GAAgB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAA,kBAAQ,EAAC,IAAI,CAAC,GAAG,EAAE;YACf,CAAC,kCAAkC,CAAC,CAAC,IAAI;gBACrC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE;oBACxC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC;oBACzC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAA0B,CAAC,CAAC;gBACrE,IAAI,WAAW,EAAE,CAAC;oBACd,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;oBAC9B,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;YACL,CAAC;SACJ,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACK,kBAAkB,CAAC,UAAwB;QAC/C,IAAI,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YACtE,MAAM,WAAW,GAAG,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC;gBAC/C,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC;gBAC1C,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;YAChD,OAAO,WAAW,IAAI,UAAU,CAAC;QACrC,CAAC;aAAM,CAAC;YACJ,OAAO,UAAU,CAAC;QACtB,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,uBAAuB,CAAC,UAA6B;QACzD,IAAI,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5E,OAAO,SAAS,CAAC;QACrB,CAAC;aAAM,IAAI,IAAA,qCAAwB,EAAC,UAAU,CAAC,EAAE,CAAC;YAC9C,OAAO,SAAS,CAAC,CAAC,4CAA4C;QAClE,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,MAAM,aAAa,GAAG,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC;YAClE,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAClC,UAAU,CAAC,QAAmC,EAC9C,aAAa,CAChB,CAAC;YACF,OAAO,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC;aAAM,CAAC;YACJ,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,wBAAwB,CAAC,UAA8B;QAC3D,IACI,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC;YAChC,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAC5E,CAAC;YACC,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAExD,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1E,MAAM,SAAS,GAAG,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CACnC,UAAU,CAAC,QAAoC,EAC/C,SAAS,EACT,UAAU,CACb,CAAC;YACF,OAAO,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC;aAAM,IAAI,UAAU,CAAC,QAAQ,IAAI,GAAG,IAAI,IAAA,qCAAwB,EAAC,KAAK,CAAC,EAAE,CAAC;YACvE,kDAAkD;YAClD,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;YAClC,UAAU,CAAC,QAAQ,GAAG,GAAG,CAAC;YAC1B,OAAO,UAAU,CAAC;QACtB,CAAC;aAAM,CAAC;YACJ,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,mBAAmB,CAAC,QAAiC,EAAE,QAAa;QACxE,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,GAAG;gBACJ,OAAO,CAAC,QAAQ,CAAC;YACrB,KAAK,GAAG;gBACJ,OAAO,CAAC,QAAQ,CAAC;YACrB,KAAK,GAAG;gBACJ,OAAO,CAAC,QAAQ,CAAC;YACrB,KAAK,GAAG;gBACJ,OAAO,CAAC,QAAQ,CAAC;YACrB,KAAK,QAAQ;gBACT,OAAO,OAAO,QAAQ,CAAC;YAC3B,KAAK,MAAM;gBACP,OAAO,KAAK,QAAQ,CAAC;QAC7B,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACK,oBAAoB,CAAC,QAAkC,EAAE,IAAS,EAAE,KAAU;QAClF,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,IAAI;gBACL,OAAO,IAAI,IAAI,KAAK,CAAC;YACzB,KAAK,IAAI;gBACL,OAAO,IAAI,IAAI,KAAK,CAAC;YACzB,KAAK,KAAK;gBACN,OAAO,IAAI,KAAK,KAAK,CAAC;YAC1B,KAAK,KAAK;gBACN,OAAO,IAAI,KAAK,KAAK,CAAC;YAC1B,KAAK,GAAG;gBACJ,OAAO,IAAI,GAAG,KAAK,CAAC;YACxB,KAAK,IAAI;gBACL,OAAO,IAAI,IAAI,KAAK,CAAC;YACzB,KAAK,GAAG;gBACJ,OAAO,IAAI,GAAG,KAAK,CAAC;YACxB,KAAK,IAAI;gBACL,OAAO,IAAI,IAAI,KAAK,CAAC;YACzB,KAAK,IAAI;gBACL,OAAO,IAAI,IAAI,KAAK,CAAC;YACzB,KAAK,IAAI;gBACL,OAAO,IAAI,IAAI,KAAK,CAAC;YACzB,KAAK,KAAK;gBACN,OAAO,IAAI,KAAK,KAAK,CAAC;YAC1B,KAAK,GAAG;gBACJ,OAAO,IAAI,GAAG,KAAK,CAAC;YACxB,KAAK,GAAG;gBACJ,OAAO,IAAI,GAAG,KAAK,CAAC;YACxB,KAAK,GAAG;gBACJ,OAAO,IAAI,GAAG,KAAK,CAAC;YACxB,KAAK,GAAG;gBACJ,OAAO,IAAI,GAAG,KAAK,CAAC;YACxB,KAAK,GAAG;gBACJ,OAAO,IAAI,GAAG,KAAK,CAAC;YACxB,KAAK,IAAI;gBACL,OAAO,IAAI,IAAI,KAAK,CAAC;YACzB,KAAK,GAAG;gBACJ,OAAO,IAAI,GAAG,KAAK,CAAC;YACxB,KAAK,GAAG;gBACJ,OAAO,IAAI,GAAG,KAAK,CAAC;YACxB,KAAK,GAAG;gBACJ,OAAO,IAAI,GAAG,KAAK,CAAC;QAC5B,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,4BAA4B,CAAC,UAAgC;QACjE,QAAQ,UAAU,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,gBAAgB,CAAC;YACtB,KAAK,eAAe,CAAC;YACrB,KAAK,gBAAgB,CAAC;YACtB,KAAK,gBAAgB;gBACjB,OAAO,UAAU,CAAC,KAAK,CAAC;YAC5B,KAAK,eAAe;gBAChB,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACpC,KAAK,iBAAiB;gBAClB,OAAO,CAAC,IAAI,CAAC,4BAA4B,CACrC,UAAU,CAAC,QAAmE,CACjF,CAAC;YACN,KAAK,aAAa;gBACd,OAAO,IAAI,CAAC;YAChB,KAAK,YAAY;gBACb,OAAO,SAAS,CAAC;YACrB,KAAK,iBAAiB;gBAClB,OAAO,EAAE,CAAC;YACd,KAAK,kBAAkB;gBACnB,OAAO,EAAE,CAAC;QAClB,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,wBAAwB,CAAC,KAAU;QACvC,QAAQ,OAAO,KAAK,EAAE,CAAC;YACnB,KAAK,QAAQ;gBACT,OAAO,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAClC,KAAK,QAAQ;gBACT,OAAO,KAAK,IAAI,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC;oBACzB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpE,KAAK,SAAS;gBACV,OAAO,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACnC,KAAK,QAAQ;gBACT,OAAO,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC7C,KAAK,WAAW;gBACZ,OAAO,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YACrC;gBACI,OAAO,SAAS,CAAC;QACzB,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,sBAAsB,CAAC,IAAY;QACvC,OAAO,CACH,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,WAAW,CAAC;YAClD,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC,CAC9D,CAAC;IACN,CAAC;;AApRL,oDAqRC;AApR0B,+BAAU,GAA6B;IAC1D,GAAG,EAAE,0BAA0B;CAClC,CAAC;AACsB,+CAA0B,GAAgB,IAAI,GAAG,CAAC;IACtE,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,QAAQ;IACR,MAAM;CACT,CAAC,CAAC;AACqB,gDAA2B,GAAgB,IAAI,GAAG,CAAC;IACvE,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,GAAG;IACH,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,GAAG;IACH,GAAG;IACH,GAAG;CACN,CAAC,CAAC"}