{"version": 3, "file": "sequenceSplitter.js", "sourceRoot": "", "sources": ["../../../../src/deobfuscator/transformations/controlFlow/sequenceSplitter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,+DAAqD;AACrD,sDAA0F;AAE1F,MAAa,gBAAiB,SAAQ,+BAAc;IAKhD;;;OAGG;IACI,OAAO,CAAC,GAAgB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAA,kBAAQ,EAAC,IAAI,CAAC,GAAG,EAAE;YACf,qBAAqB,CAAC,IAAI;gBACtB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,EAAE,CAAC;oBAC7D,MAAM,WAAW,GAAG,CAAC,CAAC,WAAW,CAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,EACd,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAC3C,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAC7C,CAAC;oBAEF,IACI,IAAI,CAAC,UAAU,CAAC,UAAU;wBAC1B,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,IAAI,WAAW;wBAC7C,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAgB,EAAE;wBAC7C,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,EAClD,CAAC;wBACC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;oBACxD,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;oBAC7C,CAAC;oBACD,IAAI,CAAC,IAAI,EAAE,CAAC;oBACZ,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;YACL,CAAC;YACD,iBAAiB,CAAC,IAAI;gBAClB,IACI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;oBAC1D,IAAI,CAAC,UAAU;oBACf,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,EACzC,CAAC;oBACC,MAAM,IAAI,GACN,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI;wBACtB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;wBAChB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACjD,MAAM,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBAEhF,IACI,IAAI,CAAC,UAAU,CAAC,UAAU;wBAC1B,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,IAAI,WAAW;wBAC7C,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAgB,EAAE;wBAC7C,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,EAClD,CAAC;wBACC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;oBACxD,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;oBAC7C,CAAC;oBACD,IAAI,CAAC,IAAI,EAAE,CAAC;oBACZ,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;YACL,CAAC;YACD,CAAC,8CAAqD,CAAC,CACnD,IAAsE;gBAEtE,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACtC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACpD,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;YACL,CAAC;YACD,WAAW,CAAC,IAAI;gBACZ,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC5C,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;oBAChE,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;gBACD,IACI,IAAI,CAAC,IAAI,CAAC,SAAS;oBACnB,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBACxC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EACvC,CAAC;oBACC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;oBAC9D,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;YACL,CAAC;YACD,mBAAmB,CAAC,IAAI;gBACpB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpC,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAChD,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAC7C,CAAC;oBAEF,IACI,IAAI,CAAC,UAAU;wBACf,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;wBAChC,IAAI,CAAC,SAAS,IAAI,MAAM,EAC1B,CAAC;wBACC,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC;wBAC3C,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;wBAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;oBAChD,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;oBAC3C,CAAC;oBACD,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;YACL,CAAC;YACD,kBAAkB,CAAC,IAAI;gBACnB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;gBAC1C,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBAC1B,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjC,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,OAAO;gBACX,CAAC;gBAED,IAAI,SAAS,GAAa,IAAI,CAAC;gBAC/B,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpC,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;oBACpC,IAAI,CAAC,MAAM,EAAE,CAAC;wBACV,OAAO;oBACX,CAAC;oBAED,IACI,CAAC,MAAM,CAAC,uBAAuB,EAAE;wBAC7B,CAAC,SAAS,CAAC,GAAG,IAAI,YAAY,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW,CAAC,CAAC;wBACpE,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,SAAS,CAAC,GAAG,IAAI,OAAO,CAAC;wBAC1D,CAAC,MAAM,CAAC,cAAc,EAAE;4BACpB,CAAC,SAAS,CAAC,GAAG,IAAI,MAAM,IAAI,SAAS,CAAC,GAAG,IAAI,QAAQ,CAAC,CAAC;wBAC3D,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,SAAS,CAAC,GAAG,IAAI,MAAM,CAAC;wBACxD,CAAC,MAAM,CAAC,yBAAyB,EAAE,IAAI,SAAS,CAAC,GAAG,IAAI,MAAM,CAAC,EACjE,CAAC;wBACC,OAAO;oBACX,CAAC;oBAED,SAAS,GAAG,MAAM,CAAC;gBACvB,CAAC;gBAED,MAAM,cAAc,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC3D,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;oBAClC,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAEvE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC9B,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAClD,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAC3B,CAAC;wBACF,SAAS,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;wBAC7C,IAAI,CAAC,UAAU,EAAE,CAAC;oBACtB,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxE,MAAM,oBAAoB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5E,SAAS,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;oBAC7C,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;oBACjC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;YACL,CAAC;SACJ,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACK,UAAU,CAAC,IAAY;QAC3B,OAAO,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC;IACvD,CAAC;;AArKL,4CAsKC;AArK0B,2BAAU,GAA6B;IAC1D,GAAG,EAAE,mBAAmB;CAC3B,CAAC"}