import * as t from '@babel/types';
/**
 * Copies an expression.
 * @param expression The expression.
 * @returns The copy.
 */
export declare const copyExpression: (expression: t.Expression) => t.Expression;
/**
 * Sets a property on an object.
 * @param obj The object.
 * @param property The property key.
 * @param value The value.
 */
export declare const setProperty: (obj: any, property: string, value: any) => void;
/**
 * Gets the value of a property on an object.
 * @param obj The object.
 * @param property The property key.
 * @returns
 */
export declare const getProperty: (obj: any, property: string) => any;
