"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deobfuscate = deobfuscate;
const parser_1 = require("@babel/parser");
const deobfuscator_1 = require("./deobfuscator/deobfuscator");
const config_1 = require("./deobfuscator/transformations/config");
/**
 * Deobfuscates a provided JS program.
 * @param source The source code.
 * @param config The deobfuscator configuration.
 * @returns The deobfuscated code.
 */
function deobfuscate(source, config = config_1.defaultConfig) {
    const ast = (0, parser_1.parse)(source);
    const deobfuscator = new deobfuscator_1.Deobfuscator(ast, config);
    const output = deobfuscator.execute();
    return output;
}
//# sourceMappingURL=index.js.map