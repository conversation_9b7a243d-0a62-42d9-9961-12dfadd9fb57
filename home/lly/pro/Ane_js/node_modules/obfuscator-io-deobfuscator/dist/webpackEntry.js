"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deobfuscate = deobfuscate;
const parser_1 = require("@babel/parser");
const deobfuscator_1 = require("./deobfuscator/deobfuscator");
/**
 * Entry point for the website to deobfuscate a script.
 * @param source The script.
 * @param config The config.
 * @returns The deobfuscated code.
 */
function deobfuscate(source, config) {
    const ast = (0, parser_1.parse)(source, { sourceType: 'unambiguous' });
    const deobfuscator = new deobfuscator_1.Deobfuscator(ast, config);
    const output = deobfuscator.execute();
    return output;
}
//# sourceMappingURL=webpackEntry.js.map