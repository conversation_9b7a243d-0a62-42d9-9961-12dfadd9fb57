#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const parser_1 = require("@babel/parser");
const commander_1 = require("commander");
const fs_1 = __importDefault(require("fs"));
const deobfuscator_1 = require("./deobfuscator/deobfuscator");
const config_1 = require("./deobfuscator/transformations/config");
const pkg = require('../package.json');
commander_1.program
    .name(pkg.name)
    .description(pkg.description)
    .version(pkg.version)
    .usage('<input_path> -o [output_path]')
    .argument('<input_path>', 'file to deobfuscate')
    .option('-o, --output [output_path]', 'output file path', 'deobfuscated.js')
    .option('-s, --silent', 'emit nothing to stdout')
    .action((input, options) => {
    const source = fs_1.default.readFileSync(input).toString();
    const ast = (0, parser_1.parse)(source, { sourceType: 'unambiguous' });
    const deobfuscator = new deobfuscator_1.Deobfuscator(ast, Object.assign(Object.assign({}, config_1.defaultConfig), { silent: !!options.silent }));
    const output = deobfuscator.execute();
    fs_1.default.writeFileSync(options.output, output);
    if (!options.silent) {
        console.log(`Wrote deobfuscated file to ${options.output}`);
    }
});
commander_1.program.parse();
