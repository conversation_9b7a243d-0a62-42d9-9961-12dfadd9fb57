"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const parser_1 = require("@babel/parser");
const fs_1 = __importDefault(require("fs"));
const deobfuscator_1 = require("./deobfuscator/deobfuscator");
const source = fs_1.default.readFileSync('input/source.js').toString();
const ast = (0, parser_1.parse)(source, { sourceType: 'unambiguous' });
const deobfuscator = new deobfuscator_1.Deobfuscator(ast);
const output = deobfuscator.execute();
fs_1.default.writeFileSync('output/output.js', output);
//# sourceMappingURL=test.js.map