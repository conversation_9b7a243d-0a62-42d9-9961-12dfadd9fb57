# VSCode 调试配置说明

本项目已配置了完整的VSCode调试环境，包含多种调试场景。**已修复TypeScript断点不生效的问题！**

## 🔧 修复内容

1. **启用了源码映射**: 在 `tsconfig.json` 中启用了 `sourceMap: true`
2. **优化了调试配置**: 所有配置都添加了 `sourceMaps: true` 和正确的 `outFiles` 路径
3. **添加了专用配置**: 新增了专门的TypeScript源码映射调试配置

## 调试配置列表

### 1. Debug CLI (TypeScript) ⭐ 推荐
- **用途**: 直接调试TypeScript源码的CLI程序
- **特点**: 使用ts-node，支持断点调试
- **要求**: 需要安装ts-node
- **断点**: ✅ 支持在.ts文件中设置断点

### 2. Debug CLI (Compiled JS) ⭐ 推荐
- **用途**: 调试编译后的JavaScript，但断点映射到TypeScript源码
- **特点**: 性能更好，完全支持源码映射
- **要求**: 会自动运行编译任务
- **断点**: ✅ 支持在.ts文件中设置断点

### 3. Debug CLI (TS with Source Maps) 🆕
- **用途**: 专门优化的TypeScript调试配置
- **特点**: 最佳的TypeScript调试体验
- **要求**: 自动编译并生成源码映射
- **断点**: ✅ 完美支持TypeScript断点

### 4. Debug Test (TypeScript)
- **用途**: 直接调试TypeScript测试文件
- **特点**: 快速测试和调试功能
- **断点**: ✅ 支持在.ts文件中设置断点

### 5. Debug Test (Compiled JS)
- **用途**: 调试编译后的测试文件
- **特点**: 会自动运行测试编译任务
- **断点**: ✅ 支持在.ts文件中设置断点

### 6. Debug String Revealer
- **用途**: 专门用于调试字符串解混淆功能
- **特点**: 针对字符串解混淆的专用调试配置
- **断点**: ✅ 支持TypeScript断点

### 7. Attach to Node Process
- **用途**: 附加到已运行的Node.js进程
- **特点**: 用于调试已启动的服务或长时间运行的进程
- **断点**: ✅ 支持源码映射

## 前置要求

### 安装ts-node (推荐)
```bash
npm install -g ts-node
# 或者在项目中安装
npm install --save-dev ts-node
```

### 安装类型定义 (如果需要)
```bash
npm install --save-dev @types/node
```

## 使用方法

### 1. 快速开始 (推荐)
1. 在VSCode中打开项目
2. 在 `src/cli.ts` 文件中设置断点（点击行号左侧）
3. 按 **F5** 或选择 "Debug CLI (TS with Source Maps)" 配置
4. 程序会自动编译并在断点处停止

### 2. 断点测试
为了验证断点是否生效，可以在以下位置设置断点：
- `src/cli.ts` 第18行：`const source = fs.readFileSync(input).toString();`
- `src/cli.ts` 第21行：`const deobfuscator = new Deobfuscator(...);`
- `src/deobfuscator/deobfuscator.ts` 中的任何方法

### 3. 设置断点
- 在代码行号左侧点击设置断点
- 红色圆点表示断点已设置
- 支持条件断点和日志断点
- **现在TypeScript文件中的断点完全有效！**

### 3. 调试控制
- **F5**: 继续执行
- **F10**: 单步跳过
- **F11**: 单步进入
- **Shift+F11**: 单步跳出
- **Ctrl+Shift+F5**: 重启调试

### 4. 自定义输入文件
调试配置默认使用`example.js`作为输入文件。要使用其他文件：
1. 修改launch.json中的args参数
2. 或者使用"Debug CLI with Inspector"任务，它会提示输入文件路径

## 示例文件

项目包含一个`example.js`示例混淆文件，可以直接用于测试调试功能。

## 调试技巧

### 1. 查看变量
- 鼠标悬停在变量上查看值
- 使用"变量"面板查看所有局部变量
- 使用"监视"面板添加表达式监视

### 2. 调用堆栈
- 在"调用堆栈"面板查看函数调用链
- 点击堆栈帧可以跳转到对应代码位置

### 3. 调试控制台
- 在调试时可以在控制台执行JavaScript表达式
- 用于快速测试和查看变量值

### 4. 条件断点
- 右键点击断点设置条件
- 只有满足条件时才会停止执行

## 故障排除

### 1. 断点不生效 ✅ 已修复
**问题**: TypeScript文件中的断点不生效
**解决方案**:
- ✅ 已在 `tsconfig.json` 中启用 `sourceMap: true`
- ✅ 已在调试配置中添加 `sourceMaps: true`
- ✅ 已添加正确的 `outFiles` 路径映射

### 2. ts-node未找到
```bash
npm install -g ts-node
# 或
npm install --save-dev ts-node
```

### 3. 编译错误
确保TypeScript配置正确：
```bash
npm run prepare
```

### 4. 源码映射文件缺失
如果断点仍然不生效，检查是否存在 `.js.map` 文件：
```bash
ls -la dist/*.js.map
```
如果没有，重新编译：
```bash
npm run prepare
```

### 5. 端口占用
如果9229端口被占用，修改launch.json中的端口号。

### 6. 断点显示为灰色
这通常表示源码映射有问题：
1. 确保项目已编译：`npm run prepare`
2. 检查 `dist/` 目录中是否有对应的 `.js.map` 文件
3. 重启VSCode调试会话

## 配置文件说明

- **launch.json**: 调试配置
- **tasks.json**: 构建任务配置  
- **settings.json**: VSCode工作区设置
- **example.js**: 示例混淆文件

## 进阶使用

### 1. 远程调试
使用"Attach to Node Process"配置可以附加到远程Node.js进程。

### 2. 性能分析
在调试时可以使用Chrome DevTools进行性能分析。

### 3. 源码映射
TypeScript调试会自动使用源码映射，可以直接在.ts文件中调试。
