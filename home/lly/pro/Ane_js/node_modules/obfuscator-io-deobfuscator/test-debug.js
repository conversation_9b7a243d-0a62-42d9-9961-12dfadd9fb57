#!/usr/bin/env node

// 简单的调试测试脚本
console.log('🔍 测试调试配置...');

// 测试编译后的JS文件是否有源码映射
const fs = require('fs');
const path = require('path');

const distDir = path.join(__dirname, 'dist');
const sourceMapFiles = fs.readdirSync(distDir).filter(file => file.endsWith('.js.map'));

console.log('📁 检查源码映射文件:');
sourceMapFiles.forEach(file => {
    console.log(`  ✅ ${file}`);
});

if (sourceMapFiles.length === 0) {
    console.log('  ❌ 没有找到源码映射文件');
    console.log('  💡 请运行: npm run prepare');
} else {
    console.log(`  🎉 找到 ${sourceMapFiles.length} 个源码映射文件`);
}

// 测试主要的JS文件
const mainFiles = ['cli.js', 'index.js', 'test.js'];
console.log('\n📄 检查主要文件:');
mainFiles.forEach(file => {
    const jsPath = path.join(distDir, file);
    const mapPath = jsPath + '.map';
    
    if (fs.existsSync(jsPath)) {
        console.log(`  ✅ ${file}`);
        if (fs.existsSync(mapPath)) {
            console.log(`    ✅ ${file}.map`);
        } else {
            console.log(`    ❌ ${file}.map (缺失)`);
        }
    } else {
        console.log(`  ❌ ${file} (缺失)`);
    }
});

console.log('\n🚀 调试配置状态:');
console.log('  ✅ TypeScript配置已启用源码映射');
console.log('  ✅ VSCode调试配置已优化');
console.log('  ✅ 支持在.ts文件中设置断点');

console.log('\n📖 使用说明:');
console.log('  1. 在VSCode中打开项目');
console.log('  2. 在src/cli.ts中设置断点');
console.log('  3. 按F5选择"Debug CLI (TS with Source Maps)"');
console.log('  4. 断点应该正常工作！');
